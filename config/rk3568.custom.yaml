# # 自定义代码配置文件 - RK3568 产品
# # 基于 feedback-enhanced 的自定义代码替换例子

# version: "1.0"
# product_name: "rk3568"  # 与主配置文件中的product_name一致

# # 代码修改配置
# code_modifications:
#   # 函数替换配置
#   function_replacements:
#     # 替换 exif 节点中的 DeliverBuffer 函数
#     - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_exif_node.cpp"
#       function_signature: "void MyExifNode::DeliverBuffer(std::shared_ptr<IBuffer>& buffer)"
#       description: "自定义 EXIF 节点缓冲区处理逻辑，添加 EXIF 数据提取和处理功能"
#       replacement_code: |
#         void MyExifNode::DeliverBuffer(std::shared_ptr<IBuffer>& buffer)
#         {
#             if (buffer == nullptr) {
#                 CAMERA_LOGE("MyExifNode::DeliverBuffer buffer is null");
#                 return;
#             }

#             // 检查缓冲区状态
#             if (buffer->GetBufferStatus() != CAMERA_BUFFER_STATUS_OK) {
#                 CAMERA_LOGE("MyExifNode::DeliverBuffer buffer status error: %d", buffer->GetBufferStatus());
#                 return NodeBase::DeliverBuffer(buffer);
#             }

#             // 获取缓冲区信息
#             int32_t bufferSize = buffer->GetSize();
#             void* bufferData = buffer->GetVirAddress();
            
#             CAMERA_LOGI("MyExifNode::DeliverBuffer processing buffer, size: %d", bufferSize);

#             // 自定义 EXIF 数据处理
#             if (bufferData != nullptr && bufferSize > 0) {
#                 // 提取 EXIF 数据
#                 ExifInfo exifInfo;
#                 if (ExtractExifData(bufferData, bufferSize, &exifInfo)) {
#                     CAMERA_LOGI("EXIF data extracted successfully");
                    
#                     // 处理 EXIF 数据
#                     ProcessExifData(&exifInfo);
                    
#                     // 更新缓冲区元数据
#                     UpdateBufferMetadata(buffer, &exifInfo);
#                 } else {
#                     CAMERA_LOGW("Failed to extract EXIF data from buffer");
#                 }
                
#                 // 应用自定义图像处理
#                 if (ApplyCustomImageProcessing(bufferData, bufferSize)) {
#                     CAMERA_LOGI("Custom image processing applied successfully");
#                 } else {
#                     CAMERA_LOGW("Custom image processing failed");
#                 }
#             }

#             // 缓冲区转储（用于调试）
#             CameraDumper& dumper = CameraDumper::GetInstance();
#             dumper.DumpBuffer("board_MyExifNode_custom", ENABLE_MYEXIF_NODE_CONVERTED, buffer);

#             // 传递缓冲区到下一个节点
#             return NodeBase::DeliverBuffer(buffer);
#         }
#       includes:
#         - "#include \"exif_processor.h\""
#         - "#include \"custom_image_processor.h\""
#         - "#include <cstring>"

#     # 替换 exif 节点中的 Config 函数
#     - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_exif_node.cpp"
#       function_signature: "RetCode MyExifNode::Config(const int32_t streamId, const CaptureMeta& meta)"
#       description: "自定义 EXIF 节点配置逻辑，设置 EXIF 参数"
#       replacement_code: |
#         RetCode MyExifNode::Config(const int32_t streamId, const CaptureMeta& meta)
#         {
#             CAMERA_LOGI("MyExifNode::Config streamId = %d", streamId);
            
#             if (meta == nullptr) {
#                 CAMERA_LOGE("MyExifNode::Config meta is nullptr");
#                 return RC_ERROR;
#             }

#             // 初始化 EXIF 处理器
#             if (!InitializeExifProcessor()) {
#                 CAMERA_LOGE("Failed to initialize EXIF processor");
#                 return RC_ERROR;
#             }

#             // 配置 EXIF 参数
#             ExifConfig exifConfig;
#             exifConfig.streamId = streamId;
#             exifConfig.imageWidth = meta->GetWidth();
#             exifConfig.imageHeight = meta->GetHeight();
#             exifConfig.imageFormat = meta->GetFormat();
#             exifConfig.enableGpsInfo = true;
#             exifConfig.enableTimestamp = true;
            
#             // 设置相机制造商信息
#             strncpy(exifConfig.cameraMake, "Custom Camera", sizeof(exifConfig.cameraMake) - 1);
#             strncpy(exifConfig.cameraModel, "RK3568 Camera", sizeof(exifConfig.cameraModel) - 1);
            
#             if (!ConfigureExifProcessor(&exifConfig)) {
#                 CAMERA_LOGE("Failed to configure EXIF processor");
#                 return RC_ERROR;
#             }

#             // 配置自定义图像处理参数
#             ImageProcessConfig imgConfig;
#             imgConfig.enableSharpening = true;
#             imgConfig.enableNoiseReduction = true;
#             imgConfig.sharpeningLevel = 0.5f;
#             imgConfig.noiseReductionLevel = 0.3f;
            
#             if (!ConfigureImageProcessor(&imgConfig)) {
#                 CAMERA_LOGE("Failed to configure image processor");
#                 return RC_ERROR;
#             }

#             CAMERA_LOGI("MyExifNode::Config completed successfully");
#             return RC_OK;
#         }
#       includes:
#         - "#include \"exif_config.h\""
#         - "#include \"image_process_config.h\""

#   # 函数添加配置
#   function_additions:
#     # 添加 EXIF 数据提取函数
#     - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_exif_node.cpp"
#       position: "end_of_file"
#       description: "添加 EXIF 数据提取和处理相关的自定义函数"
#       code: |
#         // 自定义 EXIF 处理函数实现

#         bool MyExifNode::InitializeExifProcessor()
#         {
#             CAMERA_LOGI("Initializing EXIF processor");
            
#             // 初始化 EXIF 处理器
#             exifProcessor_ = std::make_unique<ExifProcessor>();
#             if (!exifProcessor_) {
#                 CAMERA_LOGE("Failed to create EXIF processor");
#                 return false;
#             }
            
#             // 初始化图像处理器
#             imageProcessor_ = std::make_unique<CustomImageProcessor>();
#             if (!imageProcessor_) {
#                 CAMERA_LOGE("Failed to create image processor");
#                 return false;
#             }
            
#             return true;
#         }

#         bool MyExifNode::ExtractExifData(void* bufferData, int32_t bufferSize, ExifInfo* exifInfo)
#         {
#             if (!bufferData || bufferSize <= 0 || !exifInfo) {
#                 CAMERA_LOGE("Invalid parameters for EXIF extraction");
#                 return false;
#             }
            
#             if (!exifProcessor_) {
#                 CAMERA_LOGE("EXIF processor not initialized");
#                 return false;
#             }
            
#             // 使用 EXIF 处理器提取数据
#             return exifProcessor_->ExtractExifData(bufferData, bufferSize, exifInfo);
#         }

#         void MyExifNode::ProcessExifData(ExifInfo* exifInfo)
#         {
#             if (!exifInfo) {
#                 return;
#             }
            
#             CAMERA_LOGI("Processing EXIF data:");
#             CAMERA_LOGI("  Image size: %dx%d", exifInfo->imageWidth, exifInfo->imageHeight);
#             CAMERA_LOGI("  ISO: %d", exifInfo->iso);
#             CAMERA_LOGI("  Exposure time: %f", exifInfo->exposureTime);
#             CAMERA_LOGI("  F-number: %f", exifInfo->fNumber);
            
#             // 可以在这里添加更多的 EXIF 数据处理逻辑
#             // 例如：数据验证、格式转换、统计信息收集等
#         }

#         void MyExifNode::UpdateBufferMetadata(std::shared_ptr<IBuffer>& buffer, ExifInfo* exifInfo)
#         {
#             if (!buffer || !exifInfo) {
#                 return;
#             }
            
#             // 更新缓冲区的元数据
#             // 这里可以将 EXIF 信息附加到缓冲区的元数据中
#             // 具体实现取决于 IBuffer 接口的设计
            
#             CAMERA_LOGI("Buffer metadata updated with EXIF information");
#         }

#         bool MyExifNode::ConfigureExifProcessor(ExifConfig* config)
#         {
#             if (!exifProcessor_ || !config) {
#                 return false;
#             }
            
#             return exifProcessor_->Configure(config);
#         }

#         bool MyExifNode::ApplyCustomImageProcessing(void* bufferData, int32_t bufferSize)
#         {
#             if (!imageProcessor_ || !bufferData || bufferSize <= 0) {
#                 return false;
#             }
            
#             return imageProcessor_->ProcessImage(bufferData, bufferSize);
#         }

#         bool MyExifNode::ConfigureImageProcessor(ImageProcessConfig* config)
#         {
#             if (!imageProcessor_ || !config) {
#                 return false;
#             }
            
#             return imageProcessor_->Configure(config);
#         }
#       includes:
#         - "#include <memory>"
#         - "#include \"exif_processor.h\""
#         - "#include \"custom_image_processor.h\""

#   # 头文件修改配置
#   header_modifications:
#     # 修改 exif 节点头文件，添加自定义成员和方法声明
#     - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_exif_node.h"
#       description: "添加自定义 EXIF 节点的成员变量和方法声明"
#       additions:
#         - position: "private_members"
#           code: |
#             // 自定义 EXIF 处理相关成员
#             std::unique_ptr<ExifProcessor> exifProcessor_;
#             std::unique_ptr<CustomImageProcessor> imageProcessor_;
            
#             // 自定义方法声明
#             bool InitializeExifProcessor();
#             bool ExtractExifData(void* bufferData, int32_t bufferSize, ExifInfo* exifInfo);
#             void ProcessExifData(ExifInfo* exifInfo);
#             void UpdateBufferMetadata(std::shared_ptr<IBuffer>& buffer, ExifInfo* exifInfo);
#             bool ConfigureExifProcessor(ExifConfig* config);
#             bool ApplyCustomImageProcessing(void* bufferData, int32_t bufferSize);
#             bool ConfigureImageProcessor(ImageProcessConfig* config);

# # 使用说明:
# # 1. 确保此文件命名为 config/rk3568.custom.yaml
# # 2. 运行代码生成命令: python -m src.oh_codegen rk3568.yaml --clean
# # 3. 自定义修改会在代码生成完成后自动应用
# # 4. 检查生成的文件确认修改是否正确应用
