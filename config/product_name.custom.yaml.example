# 自定义代码配置文件示例
# 文件命名规范: {product_name}.custom.yaml
# 例如: rk3568.custom.yaml, hi3516dv300.custom.yaml

version: "1.0"
product_name: "your_product_name"  # 应与主配置文件中的product_name一致

# 代码修改配置
code_modifications:
  
  # 函数替换配置
  function_replacements:
    - file_path: "output/5.0.0/{product_name}/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_preview_node.cpp"
      function_signature: "RetCode MyPreviewNode::Stop(const int32_t streamId)"
      description: "自定义预览节点停止逻辑"
      replacement_code: |
        RetCode MyPreviewNode::Stop(const int32_t streamId)
        {
            CAMERA_LOGI("Custom Stop implementation for preview stream %d", streamId);
            
            // 自定义停止逻辑
            if (streamId < 0) {
                CAMERA_LOGE("Invalid stream ID: %d", streamId);
                return RC_ERROR;
            }
            
            // 执行清理工作
            CleanupResources();
            
            CAMERA_LOGI("Preview node stopped successfully");
            return RC_OK;
        }
      includes:
        - "#include \"custom_preview_utils.h\""

    - file_path: "output/5.0.0/{product_name}/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.cpp"
      function_signature: "RetCode MyCaptureNode::Start(const int32_t streamId)"
      description: "自定义捕获节点启动逻辑"
      replacement_code: |
        RetCode MyCaptureNode::Start(const int32_t streamId)
        {
            CAMERA_LOGI("Custom Start implementation for capture stream %d", streamId);
            
            // 初始化自定义参数
            if (!InitializeCustomParams()) {
                CAMERA_LOGE("Failed to initialize custom parameters");
                return RC_ERROR;
            }
            
            // 启动捕获流
            return StartCaptureStream(streamId);
        }
      includes:
        - "#include \"custom_capture_params.h\""

  # 函数添加配置
  function_additions:
    - file_path: "output/5.0.0/{product_name}/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_preview_node.cpp"
      position: "end_of_file"
      description: "添加自定义清理资源函数"
      code: |
        void MyPreviewNode::CleanupResources()
        {
            CAMERA_LOGI("Cleaning up preview node resources");
            
            // 释放缓冲区
            if (previewBuffer_) {
                free(previewBuffer_);
                previewBuffer_ = nullptr;
            }
            
            // 关闭设备句柄
            if (deviceHandle_ >= 0) {
                close(deviceHandle_);
                deviceHandle_ = -1;
            }
            
            CAMERA_LOGI("Preview node resources cleaned up");
        }
      includes:
        - "#include <unistd.h>"

    - file_path: "output/5.0.0/{product_name}/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.cpp"
      position: "end_of_file"
      description: "添加自定义参数初始化函数"
      code: |
        bool MyCaptureNode::InitializeCustomParams()
        {
            CAMERA_LOGI("Initializing custom capture parameters");
            
            // 设置自定义分辨率
            customWidth_ = 1920;
            customHeight_ = 1080;
            
            // 设置自定义格式
            customFormat_ = V4L2_PIX_FMT_NV21;
            
            // 验证参数
            if (customWidth_ <= 0 || customHeight_ <= 0) {
                CAMERA_LOGE("Invalid custom resolution: %dx%d", customWidth_, customHeight_);
                return false;
            }
            
            CAMERA_LOGI("Custom parameters initialized: %dx%d, format=0x%x", 
                       customWidth_, customHeight_, customFormat_);
            return true;
        }

        RetCode MyCaptureNode::StartCaptureStream(const int32_t streamId)
        {
            CAMERA_LOGI("Starting capture stream %d with custom parameters", streamId);
            
            // 使用自定义参数启动流
            // 这里添加具体的启动逻辑
            
            return RC_OK;
        }

    # 音频模块自定义示例
    - file_path: "output/5.0.0/{product_name}/board/audio_drivers/codec/{codec_chip}_codec/src/{codec_chip}_codec_impl.c"
      position: "end_of_file"
      description: "添加自定义音频处理函数"
      code: |
        // 自定义音频处理函数
        int32_t CustomAudioProcess(struct AudioCard *audioCard, struct AudioPcmHwParams *hwParams)
        {
            if (audioCard == NULL || hwParams == NULL) {
                AUDIO_FUNC_LOGE("Invalid parameters");
                return HDF_ERR_INVALID_PARAM;
            }
            
            // 自定义音频处理逻辑
            AUDIO_FUNC_LOGI("Custom audio processing for %s", audioCard->cardServiceName);
            
            return HDF_SUCCESS;
        }

  # 头文件修改配置
  header_modifications:
    - file_path: "output/5.0.0/{product_name}/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_preview_node.h"
      description: "添加自定义预览节点声明"
      additions:
        - position: "private_members"
          code: |
            // 自定义成员变量
            void* previewBuffer_;
            int deviceHandle_;
            
            // 自定义方法声明
            void CleanupResources();

    - file_path: "output/5.0.0/{product_name}/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.h"
      description: "添加自定义捕获节点声明"
      additions:
        - position: "private_members"
          code: |
            // 自定义参数
            int customWidth_;
            int customHeight_;
            uint32_t customFormat_;
            
            // 自定义方法声明
            bool InitializeCustomParams();
            RetCode StartCaptureStream(const int32_t streamId);

# 使用说明:
# 1. 将此文件复制为 config/{your_product_name}.custom.yaml
# 2. 修改 product_name 为你的产品名称
# 3. 将 {product_name} 和 {codec_chip} 等占位符替换为实际值
# 4. 根据需要添加、修改或删除配置项
# 5. 运行代码生成命令，自定义修改会自动应用
