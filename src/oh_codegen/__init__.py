"""
OpenHarmony Device Code Generator - Simplified Version

This is a tool for generating OpenHarmony device driver code, supporting code generation
based on Jinja2 templates and YAML configuration.

Main Features:
- Parse YAML configuration files
- Scan and render Jinja2 templates
- Execute copy_file.sh scripts
- Generate device driver code files
- Support multi-module conditional generation

Usage Example:
    from oh_codegen import generate_device_code

    # Generate with one line of code
    result = generate_device_code("orangepi_5b.yaml")

    if result['success']:
        print(f"Successfully generated {len(result['generated_files'])} files")
    else:
        print(f"Generation failed: {result['errors']}")

Advanced Usage:
    from oh_codegen import SimpleCodeGenerator

    generator = SimpleCodeGenerator()
    generator.load_config("orangepi_5b.yaml")
    result = generator.generate_code("orangepi_5b.yaml")
"""

__version__ = "2.0.0"
__author__ = "OpenHarmony Device Code Generator Team"
__email__ = "<EMAIL>"

# Import simplified version public API
from .simplified_generator import SimpleCodeGenerator, generate_device_code

__all__ = [
    # Version information
    '__version__',

    # Main API
    'SimpleCodeGenerator',
    'generate_device_code',
]