"""
Variable Mapping Configuration

Defines simplified variable mapping rules for different modules, facilitating extension and maintenance.
"""

from typing import Dict, Any, List, Callable


class VariableMappingConfig:
    """Variable mapping configuration class"""

    # Board section variable mapping configuration (example, can be extended as needed)
    BOARD_MAPPINGS = {
        # Audio related mappings
        'audio': {
            'adm_framework': {
                'condition': lambda vars: vars.get("audio_drivers_adm_drivers_enable", False),
                'mappings': {
                    "codec_chip": "audio_drivers_adm_drivers_codec_chip",
                    "compatible_driver": "audio_drivers_adm_drivers_compatible_driver",
                    "bus_type": "audio_drivers_adm_drivers_bus_type"
                }
            },
            'alsa_framework': {
                'condition': lambda vars: vars.get("audio_drivers_alsa_drivers_enable", False),
                'mappings': {
                    "codec_chip": "audio_drivers_alsa_drivers_codec_chip",
                    "implementation_method": "audio_drivers_alsa_drivers_implementation_method"
                },
                'post_process': lambda vars: vars.setdefault(
                    "compatible_driver", 
                    vars.get("audio_drivers_alsa_drivers_compatible_driver", vars.get("codec_chip", ""))
                )
            }
        },
        
        # Camera related mappings
        'camera': {
            'basic': {
                'condition': lambda vars: True,  # Always apply
                'mappings': {
                    "camera_chip": "camera_drivers_camera_chip",
                    "sensor": "camera_drivers_sensor",
                    "nodes": "camera_drivers_node",
                    "camera_method": "camera_drivers_method"
                }
            }
        },

        # Kernel related mappings
        'kernel': {
            'basic': {
                'condition': lambda vars: True,
                'mappings': {
                    "linux_kernel_version": "kernel_linux_kernel_version",
                    "if_use_specific_kernel": "kernel_if_use_specific_kernel",
                    "if_use_kernel_patch": "kernel_if_use_kernel_patch",
                    "patch_path": "kernel_patch_path"
                }
            }
        }
    }
    
    # SoC section variable mapping configuration (example, can be extended as needed)
    SOC_MAPPINGS = {
        # Display related mappings
        'display': {
            'drm_framework': {
                'condition': lambda vars: vars.get("soc_display_drm_enable", False),
                'mappings': {
                    "display_driver": "soc_display_drm_driver",
                    "display_format": "soc_display_drm_format",
                    "display_resolution": "soc_display_drm_resolution"
                }
            },
            'framebuffer': {
                'condition': lambda vars: vars.get("soc_display_fb_enable", False),
                'mappings': {
                    "display_driver": "soc_display_fb_driver",
                    "display_format": "soc_display_fb_format",
                    "display_resolution": "soc_display_fb_resolution"
                }
            }
        },
        
        # GPU related mappings
        'gpu': {
            'basic': {
                'condition': lambda vars: True,
                'mappings': {
                    "gpu_vendor": "soc_gpu_vendor",
                    "gpu_arch": "soc_gpu_architecture",
                    "opengl_version": "soc_gpu_opengl_version",
                    "vulkan_support": "soc_gpu_vulkan_support"
                }
            }
        },

        # Memory controller related mappings
        'memory': {
            'basic': {
                'condition': lambda vars: True,
                'mappings': {
                    "memory_controller": "soc_memory_controller_type",
                    "memory_frequency": "soc_memory_frequency",
                    "memory_channels": "soc_memory_channels"
                }
            }
        }
    }
    
    @classmethod
    def get_board_mappings(cls) -> Dict[str, Any]:
        """Get Board section mapping configuration"""
        return cls.BOARD_MAPPINGS

    @classmethod
    def get_soc_mappings(cls) -> Dict[str, Any]:
        """Get SoC section mapping configuration"""
        return cls.SOC_MAPPINGS

    @classmethod
    def apply_mappings(cls, variables: Dict[str, Any], mappings: Dict[str, Any]):
        """
        Apply mapping configuration

        Args:
            variables: Variable dictionary
            mappings: Mapping configuration
        """
        for module_name, module_config in mappings.items():
            for framework_name, framework_config in module_config.items():
                condition = framework_config.get('condition')
                if condition and condition(variables):
                    # Apply basic mappings
                    basic_mappings = framework_config.get('mappings', {})
                    for simplified_name, full_name in basic_mappings.items():
                        if full_name in variables:
                            variables[simplified_name] = variables[full_name]

                    # Execute post-processing
                    post_process = framework_config.get('post_process')
                    if post_process:
                        post_process(variables)

                    # For mutually exclusive frameworks (like ADM vs ALSA), stop after finding the first match
                    if module_name in ['audio'] and framework_name in ['adm_framework', 'alsa_framework']:
                        break
