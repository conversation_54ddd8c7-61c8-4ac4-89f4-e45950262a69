"""
Simplified Device Code Generator

Integrates functionality originally scattered across multiple classes into one main generator,
making the code structure clearer and easier to understand and maintain.
"""

from pathlib import Path
from typing import Dict, Any, List, Optional
import logging
import shutil
import yaml
from jinja2 import Environment, FileSystemLoader
from datetime import datetime

from .processors import get_processor, BOARD_PROCESSORS
from .processors.board.generic_processor import GenericProcessor
from .processors.soc import get_soc_processor, SOC_PROCESSORS
from .processors.soc.generic_processor import SoCGenericProcessor
from .processors.vendor import get_vendor_processor, VENDOR_PROCESSORS
from .processors.vendor.generic_processor import VendorGenericProcessor
from .variable_mappings import VariableMappingConfig
from .custom_code import CustomCodeConfig, CodeModifier

logger = logging.getLogger(__name__)


class SimpleCodeGenerator:
    """Simplified Code Generator - Integrates all core functionality"""

    def __init__(self, project_root: Optional[Path] = None):
        """
        Initialize generator

        Args:
            project_root: Project root directory, auto-detected by default
        """
        if project_root is None:
            project_root = Path(__file__).parent.parent.parent

        self.project_root = Path(project_root)
        self.config_dir = self.project_root / "config"
        self.template_dir = self.project_root / "template"
        self.output_dir = self.project_root / "output"

        # Initialize Jinja2 environment
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            trim_blocks=True,
            lstrip_blocks=True,
            keep_trailing_newline=True,
        )
        self._setup_jinja_filters()

        # Configuration data
        self.config_data = {}
        self.template_variables = {}

        logger.info(f"Simplified code generator initialized, project root: {self.project_root}")

    def _setup_jinja_filters(self):
        """Setup Jinja2 custom filters"""

        def snake_to_camel(value):
            if not value:
                return value
            components = str(value).split("_")
            return components[0] + "".join(word.capitalize() for word in components[1:])

        def format_include_guard(value):
            if not value:
                return value
            import re

            guard = re.sub(r"[^A-Za-z0-9_]", "_", str(value))
            guard = guard.upper().strip("_")
            guard = re.sub(r"_+", "_", guard)
            return f"{guard}_H_"

        # Register filters
        self.jinja_env.filters.update(
            {
                "snake_to_camel": snake_to_camel,
                "include_guard": format_include_guard,
                "upper": lambda x: str(x).upper() if x else x,
                "lower": lambda x: str(x).lower() if x else x,
            }
        )

    def load_config(self, config_file: str) -> Dict[str, Any]:
        """
        Load configuration file

        Args:
            config_file: Configuration file name (e.g., 'orangepi_5b.yaml')

        Returns:
            Configuration data dictionary
        """
        config_path = self.config_dir / config_file

        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file does not exist: {config_path}")

        try:
            with open(config_path, "r", encoding="utf-8") as f:
                self.config_data = yaml.safe_load(f)

            # Build template variables
            self.template_variables = self._build_template_variables()

            logger.info(f"Configuration loaded successfully: {config_file}")
            logger.debug(f"Template variables: {self.template_variables}")

            return self.config_data

        except Exception as e:
            logger.error(f"Failed to load configuration file: {e}")
            raise

    def _build_template_variables(self) -> Dict[str, Any]:
        """Build template variables dictionary"""
        variables = {
            # Basic information
            "product_name": self.config_data.get("product_name", ""),
            "device_company": self.config_data.get("device_company", ""),
            "product_company": self.config_data.get("device_company", ""),  # product_company 与 device_company 相同
            "device_name": self.config_data.get("product_name", ""),  # device_name 与 product_name 相同
            "soc": self.config_data.get("soc", ""),
            "soc_company": self.config_data.get("soc_company", ""),
            "target_cpu": self.config_data.get("target_cpu", ""),
            "ohos_version": self.config_data.get("ohos_version", ""),
            "ohos_path": self.config_data.get("ohos_path", ""),
            # Timestamp
            "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "year": datetime.now().year,
        }

        # Expand board_code configuration
        board_code = self.config_data.get("board_code", [])
        if isinstance(board_code, list):
            for item in board_code:
                if isinstance(item, dict):
                    variables.update(self._flatten_dict(item))

        # Add common simplified variable names
        self._add_simplified_variables(variables)

        return variables

    def _add_simplified_variables(self, variables: Dict[str, Any]):
        """Add simplified variable names for easier template usage"""
        # Use configurable variable mapping system
        self._apply_board_variable_mappings(variables)
        # Reserve extension point for future SoC section
        # self._apply_soc_variable_mappings(variables)

    def _apply_board_variable_mappings(self, variables: Dict[str, Any]):
        """Apply Board section variable mappings"""
        board_mappings = VariableMappingConfig.get_board_mappings()
        VariableMappingConfig.apply_mappings(variables, board_mappings)

    def _apply_soc_variable_mappings(self, variables: Dict[str, Any]):
        """Apply SoC section variable mappings (future extension)"""
        soc_mappings = VariableMappingConfig.get_soc_mappings()
        VariableMappingConfig.apply_mappings(variables, soc_mappings)



    def _flatten_dict(self, d: Dict[str, Any], prefix: str = "") -> Dict[str, Any]:
        """Recursively flatten nested dictionary"""
        result = {}
        for key, value in d.items():
            new_key = f"{prefix}_{key}" if prefix else key
            if isinstance(value, dict):
                result.update(self._flatten_dict(value, new_key))
            else:
                # Special handling for kernel version numbers to preserve precision
                if key == 'linux_kernel_version' and isinstance(value, float):
                    if value == 5.1:
                        value = "5.10"
                    elif value == 4.19:
                        value = "4.19"
                    elif value == 6.6:
                        value = "6.6"
                    else:
                        value = f"{value:.2f}".rstrip('0').rstrip('.')

                # Special handling for linux_kernel_source_path variable substitution
                if key == 'linux_kernel_source_path' and isinstance(value, str):
                    # Get the kernel version for substitution
                    kernel_version = None
                    if 'linux_kernel_version' in d:
                        kernel_version = d['linux_kernel_version']
                        if isinstance(kernel_version, float) and kernel_version == 5.1:
                            kernel_version = "5.10"
                        elif isinstance(kernel_version, (int, float)):
                            kernel_version = str(kernel_version)

                    # Perform variable substitution
                    if kernel_version and '${linux_kernel_version}' in value:
                        value = value.replace('${linux_kernel_version}', f"linux-{kernel_version}")
                    elif kernel_version and '${raw_kernel_version}' in value:
                        value = value.replace('${raw_kernel_version}', kernel_version)

                result[new_key] = value
        return result

    def get_enabled_modules(self) -> List[str]:
        """Get list of enabled modules"""
        enabled_modules = []
        # make sure board_code is a list
        # it can also be soc_code or any other key
        board_code = self.config_data.get("board_code", [])

        if isinstance(board_code, list):
            for item in board_code:
                if isinstance(item, dict):
                    for module_name, module_config in item.items():
                        if isinstance(module_config, dict):
                            # For specific modules, consider enabled if configuration exists
                            if module_name in ['kernel']:
                                enabled_modules.append(module_name)
                            # For other modules, check enabled field
                            elif module_config.get("enabled", False):
                                enabled_modules.append(module_name)

        # Default enabled standard modules (always needed, no need to declare in YAML)
        default_modules = ['cfg', 'distributedhardware', 'loader', 'build_config']
        for module in default_modules:
            if module not in enabled_modules:
                enabled_modules.append(module)

        logger.info(f"Enabled modules: {enabled_modules}")
        return enabled_modules

    def get_enabled_soc_components(self) -> List[str]:
        """Get list of enabled SoC components"""
        enabled_components = []
        # Check for soc_code configuration
        soc_code = self.config_data.get("soc_code", [])

        if isinstance(soc_code, list):
            for item in soc_code:
                if isinstance(item, dict):
                    for component_name, component_config in item.items():
                        if isinstance(component_config, dict):
                            # For SoC components, check enabled field (default True)
                            if component_config.get("enabled", True):
                                enabled_components.append(component_name)

        # Default enabled SoC components (always needed)
        default_components = ['build_config']
        for component in default_components:
            if component not in enabled_components:
                enabled_components.append(component)

        if enabled_components:
            logger.info(f"Enabled SoC components: {enabled_components}")
        return enabled_components

    def get_enabled_vendor_components(self) -> List[str]:
        """Get list of enabled vendor components"""
        enabled_components = []
        # Check for vendor_code configuration
        vendor_code = self.config_data.get("vendor_code", [])

        if isinstance(vendor_code, list):
            for item in vendor_code:
                if isinstance(item, dict):
                    for component_name, component_config in item.items():
                        if isinstance(component_config, dict):
                            # For vendor components, check enabled field (default True)
                            if component_config.get("enabled", True):
                                enabled_components.append(component_name)

        # Default enabled vendor components (always needed)
        default_components = ['build_config']
        for component in default_components:
            if component not in enabled_components:
                enabled_components.append(component)

        if enabled_components:
            logger.info(f"Enabled vendor components: {enabled_components}")
        return enabled_components

    def generate_code(self, config_file: str, clean_output: bool = False) -> Dict[str, Any]:
        """
        Execute complete code generation process

        Args:
            config_file: Configuration file name
            clean_output: Whether to clean output directory

        Returns:
            Generation result information
        """
        result = {
            "success": False,
            "generated_files": [],
            "executed_scripts": [],
            "errors": [],
            "warnings": [],
        }

        try:
            # Save configuration file name for later use
            self.config_file = config_file

            # 1. Load configuration
            logger.info("Step 1: Load configuration file")
            self.load_config(config_file)

            # 2. Prepare output directory
            logger.info("Step 2: Prepare output directory")
            product_output_dir = self._prepare_output_directory(clean_output)

            # 3. Get enabled modules
            enabled_modules = self.get_enabled_modules()
            if not enabled_modules:
                result["warnings"].append("No enabled modules")
                return result

            # Add enabled modules to template variables for build_config template use
            self.template_variables['enabled_modules'] = enabled_modules

            # 4. Use modular processors to handle each enabled module
            logger.info("Step 3: Use modular processors to handle enabled modules")
            module_reports = []     # Collect all Chinese module reports
            module_reports_en = []  # Collect all English module reports

            for module_name in enabled_modules:
                module_result = self._process_module_with_processor(module_name, product_output_dir)
                result["generated_files"].extend(module_result["files"])
                result["executed_scripts"].extend(module_result["scripts"])
                result["errors"].extend(module_result["errors"])

                # Collect module reports
                if "report" in module_result and module_result["report"]:
                    module_reports.append(module_result["report"])

                if "report_en" in module_result and module_result["report_en"]:
                    module_reports_en.append(module_result["report_en"])

            # 5. Process SoC components if configured
            soc_enabled_components = self.get_enabled_soc_components()
            if soc_enabled_components:
                logger.info("Step 4: Use SoC processors to handle enabled SoC components")
                soc_output_dir = self._prepare_soc_output_directory(clean_output)

                for component_name in soc_enabled_components:
                    soc_result = self._process_soc_component_with_processor(component_name, soc_output_dir)
                    result["generated_files"].extend(soc_result["files"])
                    result["executed_scripts"].extend(soc_result["scripts"])
                    result["errors"].extend(soc_result["errors"])

                    # Collect SoC component reports
                    if "report" in soc_result and soc_result["report"]:
                        module_reports.append(soc_result["report"])

                    if "report_en" in soc_result and soc_result["report_en"]:
                        module_reports_en.append(soc_result["report_en"])

            # 6. Process vendor components if configured
            vendor_enabled_components = self.get_enabled_vendor_components()
            if vendor_enabled_components:
                logger.info("Step 5: Use vendor processors to handle enabled vendor components")
                vendor_output_dir = self._prepare_vendor_output_directory(clean_output)

                for component_name in vendor_enabled_components:
                    vendor_result = self._process_vendor_component_with_processor(component_name, vendor_output_dir)
                    result["generated_files"].extend(vendor_result["files"])
                    result["executed_scripts"].extend(vendor_result["scripts"])
                    result["errors"].extend(vendor_result["errors"])

                    # Collect vendor component reports
                    if "report" in vendor_result and vendor_result["report"]:
                        module_reports.append(vendor_result["report"])

                    if "report_en" in vendor_result and vendor_result["report_en"]:
                        module_reports_en.append(vendor_result["report_en"])

            result["success"] = len(result["errors"]) == 0

            if result["success"]:
                # 7. Generate final report (Chinese and English versions)
                logger.info("Step 6: Generate development guide report")
                self._generate_final_report(module_reports, module_reports_en, product_output_dir.parent)

                # 8. Apply custom code modifications
                logger.info("Step 7: Apply custom code modifications")
                self._apply_custom_code_modifications()

                logger.info(f"Code generation successful! Generated {len(result['generated_files'])} files")
            else:
                logger.error(f"Code generation failed, {len(result['errors'])} errors")

            return result

        except Exception as e:
            error_msg = f"Error occurred during code generation: {e}"
            result["errors"].append(error_msg)
            logger.error(error_msg)
            return result

    def _prepare_output_directory(self, clean: bool = False) -> Path:
        """Prepare output directory"""
        product_name = self.config_data.get("product_name", "unknown")
        ohos_version = self.config_data.get("ohos_version", "5.0.0")

        # Build new directory structure: output/5.0/orangepi_5b/board/
        output_dir = self.output_dir / ohos_version / product_name / "board"

        if clean and output_dir.exists():
            import shutil

            shutil.rmtree(output_dir)
            logger.info(f"Clean output directory: {output_dir}")

        output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Output directory: {output_dir}")

        return output_dir

    def _prepare_soc_output_directory(self, clean_output: bool = False) -> Path:
        """
        Prepare SoC output directory

        Args:
            clean_output: Whether to clean output directory

        Returns:
            SoC output directory path
        """
        # SoC output directory structure: output/{ohos_version}/{product_name}/soc/{soc_company}/{soc}
        product_name = self.config_data.get("product_name", "unknown")
        soc_company = self.config_data.get("soc_company", "unknown")
        soc = self.config_data.get("soc", "unknown")
        ohos_version = self.config_data.get("ohos_version", "5.0.0")

        output_dir = self.project_root / "output" / ohos_version / product_name / "soc" / soc_company / soc

        if clean_output and output_dir.exists():
            logger.info(f"Clean SoC output directory: {output_dir}")
            shutil.rmtree(output_dir)

        output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"SoC output directory: {output_dir}")

        return output_dir

    def _prepare_vendor_output_directory(self, clean_output: bool = False) -> Path:
        """
        Prepare vendor output directory

        Args:
            clean_output: Whether to clean output directory

        Returns:
            Vendor output directory path
        """
        # Vendor output directory structure: output/{ohos_version}/{product_name}/vendor/{device_company}/{product_name}
        product_name = self.config_data.get("product_name", "unknown")
        device_company = self.config_data.get("device_company", "unknown")
        ohos_version = self.config_data.get("ohos_version", "5.0.0")

        output_dir = self.project_root / "output" / ohos_version / product_name / "vendor" / device_company / product_name

        if clean_output and output_dir.exists():
            logger.info(f"Clean vendor output directory: {output_dir}")
            shutil.rmtree(output_dir)

        output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Vendor output directory: {output_dir}")

        return output_dir

    def _process_module_with_processor(self, module_name: str, output_dir: Path) -> Dict[str, List]:
        """
        Process module using modular processor

        Args:
            module_name: Module name
            output_dir: Output directory

        Returns:
            Processing result
        """
        result = {"files": [], "scripts": [], "errors": [], "report": ""}

        try:
            # Get module configuration
            module_config = self._get_module_config(module_name)
            if not module_config:
                # For default modules, create empty configuration
                if module_name in ['cfg', 'distributedhardware', 'loader', 'build_config']:
                    module_config = {}
                else:
                    result["errors"].append(f"Module configuration not found: {module_name}")
                    return result

            # Get corresponding processor
            try:
                processor_class = get_processor(module_name)
                processor = processor_class(
                    template_dir=self.template_dir,
                    jinja_env=self.jinja_env,
                    template_variables=self.template_variables,
                )
            except ValueError:
                # If no specialized processor exists, use generic processor
                # For default modules, process even if configuration is empty
                is_default_module = module_name in ['cfg', 'distributedhardware', 'loader', 'build_config']
                should_use_generic = module_config.get("enabled", is_default_module)

                if should_use_generic:
                    logger.info(f"Module {module_name} has no specialized processor, using generic processor")
                    processor = GenericProcessor(
                        template_dir=self.template_dir,
                        jinja_env=self.jinja_env,
                        template_variables=self.template_variables,
                        module_name=module_name
                    )
                else:
                    logger.info(f"Module {module_name} not enabled, skipping processing")
                    return result

            # Use processor to handle module
            process_result = processor.process(module_config, output_dir)

            # Convert result format
            result["files"] = process_result.generated_files
            result["scripts"] = process_result.executed_scripts
            result["errors"] = process_result.errors
            result["report"] = process_result.module_report        # Chinese report content
            result["report_en"] = process_result.module_report_en  # English report content

            if process_result.warnings:
                logger.warning(f"Module {module_name} processing warnings: {process_result.warnings}")

            return result

        except Exception as e:
            result["errors"].append(f"Error processing module {module_name}: {e}")
            return result

    def _process_soc_component_with_processor(self, component_name: str, output_base_dir: Path) -> Dict[str, Any]:
        """
        Process SoC component using appropriate processor

        Args:
            component_name: SoC component name
            output_base_dir: Output base directory

        Returns:
            Processing result
        """
        result = {
            "files": [],
            "scripts": [],
            "errors": [],
            "report": "",
            "report_en": ""
        }

        try:
            # Get SoC component configuration
            component_config = self._get_soc_component_config(component_name)
            if component_config is None:
                component_config = {"enabled": True}  # Default config for SoC components

            # Check if component has specialized processor
            if component_name in SOC_PROCESSORS:
                processor_class = get_soc_processor(component_name)
                if component_name == 'my_group':
                    # Generic processor needs component name
                    processor = processor_class(self.template_dir, self.jinja_env, self.template_variables, component_name)
                else:
                    processor = processor_class(self.template_dir, self.jinja_env, self.template_variables)
                logger.info(f"SoC component {component_name} has specialized processor, using {processor_class.__name__}")
            else:
                # Use generic SoC processor for unknown components
                processor = SoCGenericProcessor(self.template_dir, self.jinja_env, self.template_variables, component_name)
                logger.info(f"SoC component {component_name} has no specialized processor, using generic SoC processor")

            # Process component
            process_result = processor.process(component_config, output_base_dir)

            # Convert result format
            result["files"] = process_result.generated_files
            result["scripts"] = process_result.executed_scripts
            result["errors"] = process_result.errors
            result["report"] = process_result.module_report        # Chinese report content
            result["report_en"] = process_result.module_report_en  # English report content

            if process_result.warnings:
                logger.warning(f"SoC component {component_name} processing warnings: {process_result.warnings}")

            return result

        except Exception as e:
            result["errors"].append(f"Error processing SoC component {component_name}: {e}")
            return result

    def _get_soc_component_config(self, component_name: str) -> Optional[Dict[str, Any]]:
        """
        Get SoC component configuration

        Args:
            component_name: SoC component name

        Returns:
            SoC component configuration dictionary
        """
        soc_code = self.config_data.get("soc_code", [])
        if isinstance(soc_code, list):
            for item in soc_code:
                if isinstance(item, dict) and component_name in item:
                    return item[component_name]

        return None

    def _process_vendor_component_with_processor(self, component_name: str, output_base_dir: Path) -> Dict[str, Any]:
        """
        Process vendor component using appropriate processor

        Args:
            component_name: Vendor component name
            output_base_dir: Output base directory

        Returns:
            Processing result
        """
        result = {
            "files": [],
            "scripts": [],
            "errors": [],
            "report": "",
            "report_en": ""
        }

        try:
            # Get vendor component configuration
            component_config = self._get_vendor_component_config(component_name)
            if component_config is None:
                component_config = {"enabled": True}  # Default config for vendor components

            # Get processor for vendor component
            processor_class = get_vendor_processor(component_name)
            if component_name == 'build_config':
                # Build config processor doesn't need component name
                processor = processor_class(self.template_dir, self.jinja_env, self.template_variables)
            else:
                # Generic processor needs component name
                processor = processor_class(self.template_dir, self.jinja_env, self.template_variables, component_name)

            logger.info(f"Vendor component {component_name} using processor: {processor_class.__name__}")

            # Process component
            process_result = processor.process(component_config, output_base_dir)

            # Convert result format
            result["files"] = process_result.generated_files
            result["scripts"] = process_result.executed_scripts
            result["errors"] = process_result.errors
            result["report"] = process_result.module_report        # Chinese report content
            result["report_en"] = process_result.module_report_en  # English report content

            if process_result.warnings:
                logger.warning(f"Vendor component {component_name} processing warnings: {process_result.warnings}")

            return result

        except Exception as e:
            result["errors"].append(f"Error processing vendor component {component_name}: {e}")
            return result

    def _get_vendor_component_config(self, component_name: str) -> Optional[Dict[str, Any]]:
        """
        Get vendor component configuration

        Args:
            component_name: Vendor component name

        Returns:
            Vendor component configuration dictionary
        """
        vendor_code = self.config_data.get("vendor_code", [])
        if isinstance(vendor_code, list):
            for item in vendor_code:
                if isinstance(item, dict) and component_name in item:
                    return item[component_name]

        return None

    def _get_module_config(self, module_name: str) -> Optional[Dict[str, Any]]:
        """
        Get module configuration

        Args:
            module_name: Module name

        Returns:
            Module configuration dictionary
        """
        board_code = self.config_data.get("board_code", [])
        if isinstance(board_code, list):
            for item in board_code:
                if isinstance(item, dict) and module_name in item:
                    return item[module_name]

        return None

    def _generate_final_report(self, module_reports: List[str], module_reports_en: List[str], output_base_dir: Path):
        """Generate final development guide report (Chinese and English versions)"""

        product_name = self.config_data.get("product_name", "unknown")
        ohos_version = self.config_data.get("ohos_version", "5.0.0")
        device_company = self.config_data.get("device_company", "unknown")
        soc = self.config_data.get("soc", "unknown")

        # Generate Chinese report
        self._generate_report_by_language(module_reports, output_base_dir, 'zh',
                                        product_name, ohos_version, device_company, soc)

        # Generate English report
        self._generate_report_by_language(module_reports_en, output_base_dir, 'en',
                                        product_name, ohos_version, device_company, soc)

    def _generate_report_by_language(self, module_reports: List[str], output_base_dir: Path,
                                   language: str, product_name: str, ohos_version: str,
                                   device_company: str, soc: str):
        """Generate report in specified language"""

        report_lines = []

        if language == 'en':
            # English report header
            report_lines.extend([
                f"# OpenHarmony Device Code Generation Report",
                "",
                f"**Product Name**: {product_name}",
                f"**System Version**: OpenHarmony {ohos_version}",
                f"**Device Vendor**: {device_company}",
                f"**SoC Model**: {soc}",
                f"**Generation Time**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "",
                "---",
                "",
                "## Overview",
                "",
                "This report provides developers with key information and operational guidance needed to complete device adaptation.",
                "Please follow the guidance for each module to complete the corresponding development work.",
                "",
                "## Board Layer Module Configuration",
                ""
            ])
        else:
            # Chinese report header
            report_lines.extend([
                f"# OpenHarmony 设备代码生成报告",
                "",
                f"**产品名称**: {product_name}",
                f"**系统版本**: OpenHarmony {ohos_version}",
                f"**设备厂商**: {device_company}",
                f"**SoC 型号**: {soc}",
                f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "",
                "---",
                "",
                "## 概述",
                "",
                "本报告为开发者提供了完成设备适配所需的关键信息和操作指导。",
                "请按照各模块的指导完成相应的开发工作。",
                "",
                "## Board 层模块配置",
                ""
            ])

        # Add module reports
        for report in module_reports:
            if report.strip():
                report_lines.append(report)
                report_lines.append("")

        if language == 'en':
            # English report footer
            report_lines.extend([
                "---",
                f"*This report is automatically generated by OpenHarmony Device Code Generator*",
                f"*Generation path: {output_base_dir / 'DEVELOPMENT_GUIDE_EN.md'}*"
            ])
            report_filename = "DEVELOPMENT_GUIDE_EN.md"
        else:
            # Chinese report footer
            report_lines.extend([
                "---",
                f"*此报告由 OpenHarmony 设备代码生成器自动生成*",
                f"*生成路径: {output_base_dir / 'DEVELOPMENT_GUIDE.md'}*"
            ])
            report_filename = "DEVELOPMENT_GUIDE.md"

        # Write report file
        report_path = output_base_dir / report_filename
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))
            logger.info(f"Development guide report generated: {report_path}")
        except Exception as e:
            logger.error(f"Failed to generate report file: {e}")

    def _apply_custom_code_modifications(self):
        """Apply custom code modifications"""
        try:
            # Find custom code configuration file
            config_path = self._find_custom_config_file()

            if not config_path:
                logger.info("Custom code configuration file not found, skipping custom modifications")
                return

            # Load configuration
            custom_config = CustomCodeConfig.load_from_file(config_path)
            if not custom_config:
                logger.warning("Failed to load custom code configuration")
                return

            # Check product name match
            current_product = self.config_data.get("product_name", "")
            if custom_config.product_name and custom_config.product_name != current_product:
                logger.info(f"Custom code configuration product name ({custom_config.product_name}) "
                           f"does not match current product ({current_product}), skipping modifications")
                return

            # Apply modifications
            modifier = CodeModifier(self.project_root)
            success = modifier.apply_modifications(custom_config)

            if success:
                logger.info("Custom code modifications applied successfully")
            else:
                logger.warning("Some custom code modifications failed to apply, please check logs")

        except Exception as e:
            logger.error(f"Error applying custom code modifications: {e}")

    def _find_custom_config_file(self) -> Optional[Path]:
        """Find custom code configuration file"""
        try:
            # Get current configuration file name (without extension)
            if hasattr(self, 'config_file') and self.config_file:
                config_name = Path(self.config_file).stem

                # Option 1: Look for config/{config_name}.custom.yaml
                custom_config_path = self.project_root / "config" / f"{config_name}.custom.yaml"
                if custom_config_path.exists():
                    logger.info(f"Found product-specific custom configuration: {custom_config_path}")
                    return custom_config_path

            # Option 2: Look for custom_code_config.yaml in root directory (backward compatibility)
            fallback_config_path = self.project_root / "custom_code_config.yaml"
            if fallback_config_path.exists():
                logger.info(f"Found general custom configuration: {fallback_config_path}")
                return fallback_config_path

            # Option 3: Search by product name
            product_name = self.config_data.get("product_name", "")
            if product_name:
                product_config_path = self.project_root / "config" / f"{product_name}.custom.yaml"
                if product_config_path.exists():
                    logger.info(f"Found product name matching custom configuration: {product_config_path}")
                    return product_config_path

            return None

        except Exception as e:
            logger.error(f"Error finding custom configuration file: {e}")
            return None


# Convenience function
def generate_device_code(
    config_file: str, project_root: Optional[Path] = None, clean_output: bool = False
) -> Dict[str, Any]:
    """
    Convenient code generation function

    Args:
        config_file: Configuration file name
        project_root: Project root directory
        clean_output: Whether to clean output directory

    Returns:
        Generation result
    """
    generator = SimpleCodeGenerator(project_root)
    return generator.generate_code(config_file, clean_output)
