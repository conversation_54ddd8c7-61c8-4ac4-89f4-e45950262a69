"""
Simplified Command Line Interface

Provides simple and easy-to-use command line tools for code generation
"""

import argparse
import logging
import sys
from pathlib import Path
from .simplified_generator import SimpleCodeGenerator, generate_device_code


def setup_logging(verbose: bool = False):
    """Setup logging"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='OpenHarmony Device Code Generator - Simplified Version',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Example Usage:
  python -m oh_codegen.simple_cli orangepi_5b.yaml
  python -m oh_codegen.simple_cli orangepi_5b.yaml --clean
  python -m oh_codegen.simple_cli orangepi_5b.yaml --verbose
        """
    )
    
    parser.add_argument(
        'config_file',
        help='Configuration file name (e.g., orangepi_5b.yaml)'
    )

    parser.add_argument(
        '--clean',
        action='store_true',
        help='Clean output directory'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output'
    )

    parser.add_argument(
        '--project-root',
        type=Path,
        help='Project root directory path (auto-detected by default)'
    )

    parser.add_argument(
        '--preview',
        action='store_true',
        help='Preview mode: only show what will be generated, do not actually generate files'
    )
    
    args = parser.parse_args()

    # Setup logging
    setup_logging(args.verbose)

    try:
        if args.preview:
            # Preview mode
            generator = SimpleCodeGenerator(args.project_root)
            generator.load_config(args.config_file)

            print(f"\n=== Preview Mode ===")
            print(f"Configuration file: {args.config_file}")
            print(f"Product name: {generator.config_data.get('product_name', 'N/A')}")
            print(f"Enabled modules: {', '.join(generator.get_enabled_modules())}")
            print(f"Template variables count: {len(generator.template_variables)}")

            if args.verbose:
                print(f"\nTemplate variables:")
                for key, value in generator.template_variables.items():
                    print(f"  {key}: {value}")

            # Build correct output directory path
            product_name = generator.config_data.get('product_name', 'unknown')
            ohos_version = generator.config_data.get('ohos_version', '5.0.0')
            output_path = generator.output_dir / ohos_version / product_name / "device"
            print(f"\nOutput directory: {output_path}")
            print("Note: Preview mode will not actually generate files")
            
        else:
            # Actual generation
            print(f"Starting device code generation...")
            print(f"Configuration file: {args.config_file}")

            result = generate_device_code(
                config_file=args.config_file,
                project_root=args.project_root,
                clean_output=args.clean
            )

            # Display results
            print(f"\n=== Generation Results ===")
            print(f"Status: {'Success' if result['success'] else 'Failed'}")
            print(f"Generated files: {len(result['generated_files'])}")
            print(f"Executed scripts: {len(result['executed_scripts'])}")

            if result['warnings']:
                print(f"Warnings: {len(result['warnings'])}")
                for warning in result['warnings']:
                    print(f"  - {warning}")

            if result['errors']:
                print(f"Errors: {len(result['errors'])}")
                for error in result['errors']:
                    print(f"  - {error}")
                sys.exit(1)

            if args.verbose and result['generated_files']:
                print(f"\nGenerated files:")
                for file_path in result['generated_files']:
                    print(f"  - {file_path}")

            if args.verbose and result['executed_scripts']:
                print(f"\nExecuted scripts:")
                for script_path in result['executed_scripts']:
                    print(f"  - {script_path}")

            print(f"\nCode generation completed!")
    
    except KeyboardInterrupt:
        print("\nUser interrupted operation")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
