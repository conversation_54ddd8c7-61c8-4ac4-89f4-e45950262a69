"""
Custom Code Modification Module

Provides custom modification functionality after code generation, including function replacement, function addition, and header file modification
"""

from .config import CustomCodeConfig, FunctionReplacement, FunctionAddition, HeaderModification
from .code_modifier import CodeModifier
from .cpp_parser import CppParser

__all__ = [
    'CustomCodeConfig',
    'FunctionReplacement', 
    'FunctionAddition',
    'HeaderModification',
    'CodeModifier',
    'CppParser'
]
