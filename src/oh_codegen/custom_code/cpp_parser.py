"""
C++代码解析器

用于解析C++代码文件，查找函数定义和插入位置
"""

import re
from typing import List, Dict, Tuple, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class CppParser:
    """C++代码解析器"""

    def __init__(self):
        # 函数定义的正则表达式模式
        self.function_pattern = re.compile(
            r'^\s*(?:(?:inline|static|virtual|explicit|const|constexpr)\s+)*'  # 修饰符
            r'(?:[\w:]+\s+)*'  # 返回类型
            r'([\w:]+::\w+)\s*\([^)]*\)\s*'  # 函数名和参数
            r'(?:const\s+)?(?:override\s+)?(?:final\s+)?'  # 后缀修饰符
            r'(?:\s*:\s*[^{]+)?'  # 初始化列表
            r'\s*{',  # 开始大括号
            re.MULTILINE
        )
        
        # 类定义的正则表达式模式
        self.class_pattern = re.compile(
            r'^\s*class\s+(\w+)(?:\s*:\s*[^{]+)?\s*{',
            re.MULTILINE
        )

    def find_function_definition(self, content: str, function_signature: str) -> Optional[Tuple[int, int]]:
        """
        查找函数定义的位置
        
        Args:
            content: 文件内容
            function_signature: 函数签名，如 "RetCode MyExifNode::Stop(const int32_t streamId)"
            
        Returns:
            (start_line, end_line) 或 None
        """
        try:
            # 提取函数名
            func_name_match = re.search(r'(\w+::\w+)\s*\(', function_signature)
            if not func_name_match:
                logger.warning(f"无法从函数签名中提取函数名: {function_signature}")
                return None
            
            func_name = func_name_match.group(1)
            lines = content.split('\n')
            
            # 查找函数开始位置
            start_line = None
            for i, line in enumerate(lines):
                if func_name in line and '{' in line:
                    start_line = i
                    break
                elif func_name in line:
                    # 函数声明可能跨多行，继续查找开始大括号
                    for j in range(i, min(i + 5, len(lines))):
                        if '{' in lines[j]:
                            start_line = i
                            break
                    if start_line is not None:
                        break
            
            if start_line is None:
                logger.warning(f"未找到函数定义: {func_name}")
                return None
            
            # 查找函数结束位置（匹配大括号）
            brace_count = 0
            end_line = None
            
            for i in range(start_line, len(lines)):
                line = lines[i]
                brace_count += line.count('{') - line.count('}')
                if brace_count == 0 and '}' in line:
                    end_line = i
                    break
            
            if end_line is None:
                logger.warning(f"未找到函数结束位置: {func_name}")
                return None
            
            return (start_line, end_line)
            
        except Exception as e:
            logger.error(f"查找函数定义时出错: {e}")
            return None

    def find_class_sections(self, content: str, class_name: str) -> Dict[str, int]:
        """
        查找类的各个部分位置
        
        Args:
            content: 文件内容
            class_name: 类名
            
        Returns:
            包含各部分位置的字典
        """
        try:
            lines = content.split('\n')
            sections = {}
            
            # 查找类定义开始
            class_start = None
            for i, line in enumerate(lines):
                if re.search(rf'^\s*class\s+{class_name}\b', line):
                    class_start = i
                    break
            
            if class_start is None:
                logger.warning(f"未找到类定义: {class_name}")
                return sections
            
            # 查找public, private, protected部分
            current_section = "private"  # 默认为private
            in_class = False
            brace_count = 0
            
            for i in range(class_start, len(lines)):
                line = lines[i].strip()
                
                if '{' in line:
                    in_class = True
                    brace_count += line.count('{')
                
                if in_class:
                    brace_count += line.count('{') - line.count('}')
                    
                    if brace_count == 0:  # 类结束
                        sections['class_end'] = i
                        break
                    
                    # 检查访问修饰符
                    if line.startswith('public:'):
                        current_section = "public"
                        sections['public_start'] = i
                    elif line.startswith('private:'):
                        current_section = "private"
                        sections['private_start'] = i
                    elif line.startswith('protected:'):
                        current_section = "protected"
                        sections['protected_start'] = i
                    
                    # 记录方法和成员变量的位置
                    if current_section == "public" and ('(' in line or ';' in line):
                        if 'public_methods' not in sections:
                            sections['public_methods'] = i
                    elif current_section == "private" and ('(' in line or ';' in line):
                        if 'private_members' not in sections:
                            sections['private_members'] = i
            
            return sections
            
        except Exception as e:
            logger.error(f"查找类部分时出错: {e}")
            return {}

    def find_includes_section(self, content: str) -> int:
        """
        查找#include部分的结束位置
        
        Args:
            content: 文件内容
            
        Returns:
            include部分结束的行号
        """
        lines = content.split('\n')
        last_include_line = -1
        
        for i, line in enumerate(lines):
            if line.strip().startswith('#include'):
                last_include_line = i
        
        return last_include_line + 1 if last_include_line >= 0 else 0

    def extract_function_body(self, content: str, start_line: int, end_line: int) -> str:
        """
        提取函数体内容
        
        Args:
            content: 文件内容
            start_line: 开始行号
            end_line: 结束行号
            
        Returns:
            函数体内容
        """
        lines = content.split('\n')
        return '\n'.join(lines[start_line:end_line + 1])

    def get_indentation(self, content: str, line_number: int) -> str:
        """
        获取指定行的缩进
        
        Args:
            content: 文件内容
            line_number: 行号
            
        Returns:
            缩进字符串
        """
        lines = content.split('\n')
        if 0 <= line_number < len(lines):
            line = lines[line_number]
            return line[:len(line) - len(line.lstrip())]
        return ""
