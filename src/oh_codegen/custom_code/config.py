"""
Custom Code Configuration Data Structures

Used to manage custom modification configurations after code generation
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from pathlib import Path
import yaml
import logging

logger = logging.getLogger(__name__)


@dataclass
class FunctionReplacement:
    """Function replacement configuration"""
    file_path: str
    function_signature: str
    replacement_code: str
    includes: List[str] = field(default_factory=list)
    description: str = ""


@dataclass
class FunctionAddition:
    """Function addition configuration"""
    file_path: str
    position: str  # "end_of_class", "after_function:func_name", "before_function:func_name", "end_of_file"
    code: str
    includes: List[str] = field(default_factory=list)
    description: str = ""


@dataclass
class HeaderModification:
    """Header file modification configuration"""
    file_path: str
    additions: List[Dict[str, str]] = field(default_factory=list)  # [{"position": "public_methods", "code": "..."}]
    description: str = ""


@dataclass
class CustomCodeConfig:
    """Custom code configuration"""
    version: str = "1.0"
    product_name: str = ""
    function_replacements: List[FunctionReplacement] = field(default_factory=list)
    function_additions: List[FunctionAddition] = field(default_factory=list)
    header_modifications: List[HeaderModification] = field(default_factory=list)

    @classmethod
    def load_from_file(cls, config_path: Path) -> Optional['CustomCodeConfig']:
        """从YAML文件加载配置"""
        try:
            if not config_path.exists():
                logger.info(f"自定义代码配置文件不存在: {config_path}")
                return None

            with open(config_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)

            if not data:
                logger.warning(f"自定义代码配置文件为空: {config_path}")
                return None

            config = cls()
            config.version = data.get('version', '1.0')
            config.product_name = data.get('product_name', '')

            # 加载函数替换配置
            code_mods = data.get('code_modifications', {})
            
            for replacement_data in code_mods.get('function_replacements', []):
                replacement = FunctionReplacement(
                    file_path=replacement_data['file_path'],
                    function_signature=replacement_data['function_signature'],
                    replacement_code=replacement_data['replacement_code'],
                    includes=replacement_data.get('includes', []),
                    description=replacement_data.get('description', '')
                )
                config.function_replacements.append(replacement)

            # 加载函数添加配置
            for addition_data in code_mods.get('function_additions', []):
                addition = FunctionAddition(
                    file_path=addition_data['file_path'],
                    position=addition_data['position'],
                    code=addition_data['code'],
                    includes=addition_data.get('includes', []),
                    description=addition_data.get('description', '')
                )
                config.function_additions.append(addition)

            # 加载头文件修改配置
            for header_data in code_mods.get('header_modifications', []):
                header_mod = HeaderModification(
                    file_path=header_data['file_path'],
                    additions=header_data.get('additions', []),
                    description=header_data.get('description', '')
                )
                config.header_modifications.append(header_mod)

            logger.info(f"Successfully loaded custom code configuration: {config_path}")
            logger.info(f"Configuration contains: {len(config.function_replacements)} function replacements, "
                       f"{len(config.function_additions)} function additions, "
                       f"{len(config.header_modifications)} header modifications")

            return config

        except Exception as e:
            logger.error(f"Failed to load custom code configuration: {e}")
            return None

    def save_to_file(self, config_path: Path):
        """Save configuration to YAML file"""
        try:
            data = {
                'version': self.version,
                'product_name': self.product_name,
                'code_modifications': {
                    'function_replacements': [],
                    'function_additions': [],
                    'header_modifications': []
                }
            }

            # 转换函数替换配置
            for replacement in self.function_replacements:
                data['code_modifications']['function_replacements'].append({
                    'file_path': replacement.file_path,
                    'function_signature': replacement.function_signature,
                    'replacement_code': replacement.replacement_code,
                    'includes': replacement.includes,
                    'description': replacement.description
                })

            # 转换函数添加配置
            for addition in self.function_additions:
                data['code_modifications']['function_additions'].append({
                    'file_path': addition.file_path,
                    'position': addition.position,
                    'code': addition.code,
                    'includes': addition.includes,
                    'description': addition.description
                })

            # 转换头文件修改配置
            for header_mod in self.header_modifications:
                data['code_modifications']['header_modifications'].append({
                    'file_path': header_mod.file_path,
                    'additions': header_mod.additions,
                    'description': header_mod.description
                })

            # 确保目录存在
            config_path.parent.mkdir(parents=True, exist_ok=True)

            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True, indent=2)

            logger.info(f"Custom code configuration saved: {config_path}")

        except Exception as e:
            logger.error(f"Failed to save custom code configuration: {e}")
            raise
