"""
Build Configuration Processor

Handles code generation for build configuration files, including BUILD.gn, device.gni, ohos.build, etc.
These files are placed directly in the board directory, not in a separate subdirectory.
"""

from pathlib import Path
from typing import Dict, Any
import logging

from ..base_processor import BaseBoardProcessor, ProcessResult

logger = logging.getLogger(__name__)


class BuildConfigProcessor(BaseBoardProcessor):
    """Build Configuration Board Processor"""

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether build configuration should be processed

        Args:
            config: Build configuration

        Returns:
            True if build configuration exists, False otherwise
        """
        # Build configuration is a default module, always needs processing
        # Process even if configuration is empty (use default configuration)
        return config.get("enabled", True)

    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name

        Args:
            config: Build configuration

        Returns:
            Output directory name (empty string means directly in board directory)
        """
        # Return empty string, meaning directly in board directory
        return ''

    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        return 'build_config'
    
    def process(self, config: Dict[str, Any], output_base_dir: Path) -> ProcessResult:
        """
        Process build configuration code generation

        Args:
            config: Build configuration
            output_base_dir: Output base directory

        Returns:
            Processing result
        """
        result = ProcessResult()

        try:
            # Check if should process
            if not self.should_process(config):
                logger.info("Build configuration not enabled, skipping processing")
                result.success = True
                return result

            # Analyze build configuration
            build_info = self._analyze_build_config(config)
            logger.info(f"Build configuration: {build_info}")

            # Process module dependencies for BUILD.gn template
            self._process_module_dependencies()

            # Get output directory - directly use board directory
            output_dir_name = self.get_output_directory_name(config)
            if output_dir_name:
                output_dir = output_base_dir / output_dir_name
            else:
                output_dir = output_base_dir  # Directly use board directory

            template_dir = self.template_dir / "device" / "board" / self.get_template_directory_name()

            if not template_dir.exists():
                result.add_error(f"Build configuration template directory does not exist: {template_dir}")
                return result

            logger.info(f"Processing build configuration: {template_dir} -> {output_dir}")

            # Process template directory
            self._process_device_directory(template_dir, output_dir, result)

            # Perform special processing based on build configuration type
            self._process_build_config_specific_logic(config, output_dir, result)

            result.success = len(result.errors) == 0
            return result

        except Exception as e:
            result.add_error(f"Error processing build configuration: {e}")
            return result
    
    def _analyze_build_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze build configuration

        Args:
            config: Build configuration

        Returns:
            Analysis result
        """
        build_info = {
            'build_gn': config.get('build_gn', True),
            'device_gni': config.get('device_gni', True),
            'ohos_build': config.get('ohos_build', True),
            'enabled': True  # Build configuration exists means enabled
        }

        logger.debug(f"Build configuration analysis: {build_info}")
        return build_info

    def _process_build_config_specific_logic(self, config: Dict[str, Any], output_dir: Path, result: ProcessResult):
        """
        Process build configuration specific logic

        Args:
            config: Build configuration
            output_dir: Output directory
            result: Processing result
        """
        try:
            # Ensure output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)

            # Can add build configuration specific post-processing logic here
            # For example: validate generated BUILD.gn file syntax, etc.

            # Check if key build files are generated
            key_files = ['BUILD.gn', 'device.gni', 'ohos.build']
            for key_file in key_files:
                file_path = output_dir / key_file
                if file_path.exists():
                    logger.info(f"Build configuration file generated: {key_file}")
                else:
                    logger.warning(f"Build configuration file not generated: {key_file}")

            logger.info("Build configuration processing completed")

        except Exception as e:
            result.add_warning(f"Error processing build configuration specific logic: {e}")

    def _process_module_dependencies(self):
        """
        Process module dependencies for BUILD.gn template

        This method determines which modules should be included in the BUILD.gn deps list.
        It excludes certain modules that don't need to be in deps or have special handling.
        """
        try:
            # Get enabled modules from template variables
            enabled_modules = self.template_variables.get('enabled_modules', [])

            # Define modules that should be excluded from BUILD.gn deps
            # These modules either:
            # 1. Are handled by fixed deps in the template (cfg, distributedhardware, kernel, updater)
            # 2. Don't need BUILD.gn files (loader, build_config)
            # 3. Have special handling and shouldn't be in deps (audio_drivers, camera_drivers, uboot)
            excluded_modules = [
                'cfg',              # Fixed dep: "cfg:init_configs"
                'distributedhardware',  # Fixed dep: "distributedhardware:distributedhardware"
                'kernel',           # Fixed dep: "kernel:kernel"
                'updater',          # Fixed dep: "updater:updater_files"
                'build_config',     # This processor itself, no BUILD.gn needed
                'loader',           # Special module, no BUILD.gn file
                'audio_drivers',    # Audio modules have special handling
                'camera_drivers',   # Camera modules have special handling
                'uboot',             # U-Boot has special handling
                'bootanimation'    # Boot animation module
            ]

            # Create list of modules that need BUILD.gn dependencies
            modules_with_build_gn = []
            for module in enabled_modules:
                if module not in excluded_modules:
                    modules_with_build_gn.append(module)

            # Update template variables
            self.template_variables['modules_with_build_gn'] = modules_with_build_gn

            logger.info(f"Modules requiring BUILD.gn dependencies: {modules_with_build_gn}")
            logger.debug(f"Excluded modules: {excluded_modules}")

        except Exception as e:
            logger.error(f"Error processing module dependencies: {e}")
            # Set empty list as fallback
            self.template_variables['modules_with_build_gn'] = []
