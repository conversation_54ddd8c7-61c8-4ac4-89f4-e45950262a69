"""
Generic Module Processor

Handles code generation for generic modules, including:
- Standard modules: cfg, distributedhardware, uboot, etc. (require BUILD.gn)
- Custom modules: resources, updater, etc. (require BUILD.gn)
- Special modules: loader, etc. (do not require BUILD.gn)
"""

from pathlib import Path
from typing import Dict, Any, Optional
import logging

from ..base_processor import BaseBoardProcessor, ProcessResult

logger = logging.getLogger(__name__)


class GenericProcessor(BaseBoardProcessor):
    """Generic module Board processor"""

    # Predefined standard module configurations
    STANDARD_MODULES = {
        'cfg': {
            'template_dir': 'cfg',
            'needs_build_gn': True,
            'description': 'Configuration files (init, fstab, etc.)'
        },
        'distributedhardware': {
            'template_dir': 'distributedhardware',
            'needs_build_gn': True,
            'description': 'Distributed hardware components'
        },
        'uboot': {
            'template_dir': 'uboot',
            'needs_build_gn': True,
            'description': 'U-Boot bootloader'
        },
        'loader': {
            'template_dir': None,  # Do not use template
            'needs_build_gn': False,
            'description': 'Loader files (no BUILD.gn needed)'
        },
        'bootanimation': {
            'template_dir': 'bootanimation',
            'needs_build_gn': True,
            'description': 'Boot animation files and configuration'
        },
        'updater': {
            'template_dir': 'updater',
            'needs_build_gn': True,
            'description': 'Updater configuration files'
        }
    }

    def __init__(self, template_dir: Path, jinja_env, template_variables: Dict[str, Any], module_name: str):
        """
        Initialize generic processor

        Args:
            template_dir: Template root directory
            jinja_env: Jinja2 environment
            template_variables: Template variables dictionary
            module_name: Module name
        """
        super().__init__(template_dir, jinja_env, template_variables)
        self.module_name = module_name
        self.module_config = self.STANDARD_MODULES.get(module_name, {})
        logger.info(f"Initialize generic processor: {module_name}")

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether generic module should be processed

        Args:
            config: Module configuration

        Returns:
            True if module is enabled, False otherwise
        """
        # For standard modules, process as long as configuration exists (unless explicitly disabled)
        if self.module_name in self.STANDARD_MODULES:
            return config.get("enabled", True)  # Default enabled

        # For custom modules, need explicit enabling
        return config.get("enabled", False)

    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name

        Args:
            config: Module configuration

        Returns:
            Output directory name
        """
        return self.module_name

    def get_template_directory_name(self) -> Optional[str]:
        """
        Get template directory name

        Returns:
            Template directory name, None if no template is used
        """
        # For standard modules, use predefined template directory
        if self.module_name in self.STANDARD_MODULES:
            return self.module_config.get('template_dir')

        # 对于自定义模块，使用通用模板
        return 'my_group'
    
    def process(self, config: Dict[str, Any], output_base_dir: Path) -> ProcessResult:
        """
        处理通用模块代码生成

        Args:
            config: 模块配置
            output_base_dir: 输出基础目录

        Returns:
            处理结果
        """
        result = ProcessResult()

        try:
            # 检查是否应该处理
            if not self.should_process(config):
                logger.info(f"Generic module {self.module_name} not enabled, skipping processing")
                result.success = True
                return result

            # 分析模块配置
            module_info = self._analyze_generic_config(config)
            logger.info(f"Generic module {self.module_name} configuration: {module_info}")

            # 准备模块特定的模板变量
            self._prepare_module_variables(config)

            # 获取输出目录
            output_dir = output_base_dir / self.get_output_directory_name(config)

            # 检查是否需要处理模板
            template_dir_name = self.get_template_directory_name()
            needs_build_gn = self.module_config.get('needs_build_gn', True)

            if template_dir_name and needs_build_gn:
                # 需要处理模板的模块
                template_dir = self.template_dir / "device" / "board" / template_dir_name

                if not template_dir.exists():
                    result.add_error(f"通用模块模板目录不存在: {template_dir}")
                    return result

                logger.info(f"Processing generic module {self.module_name}: {template_dir} -> {output_dir}")

                # 处理模板目录
                self._process_device_directory(template_dir, output_dir, result)
            else:
                # Modules that don't need templates (like loader)
                logger.info(f"Processing special module {self.module_name}: only create directory -> {output_dir}")

                # 确保输出目录存在
                output_dir.mkdir(parents=True, exist_ok=True)
                # 记录创建的目录
                result.output_directories.append(str(output_dir))

            # 根据模块类型进行特殊处理
            self._process_generic_specific_logic(config, output_dir, result)

            # 生成模块报告（中文和英文版本）
            if len(result.errors) == 0:
                # 生成中文报告
                report_content_zh = self._load_generic_report_template(config, 'zh')
                result.set_module_report(self.module_name, report_content_zh)

                # 生成英文报告
                report_content_en = self._load_generic_report_template(config, 'en')
                result.set_module_report_en(self.module_name, report_content_en)

            result.success = len(result.errors) == 0
            return result

        except Exception as e:
            result.add_error(f"处理通用模块 {self.module_name} 时出错: {e}")
            return result
    
    def _analyze_generic_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析通用模块配置

        Args:
            config: 模块配置

        Returns:
            分析结果
        """
        # 获取默认描述
        default_description = self.module_config.get('description', f'{self.module_name} module')

        module_info = {
            'module_name': self.module_name,
            'module_type': 'standard' if self.module_name in self.STANDARD_MODULES else 'custom',
            'enabled': config.get('enabled', self.module_name in self.STANDARD_MODULES),
            'description': config.get('description', default_description),
            'dependencies': config.get('dependencies', []),
            'needs_build_gn': self.module_config.get('needs_build_gn', True)
        }

        logger.debug(f"通用模块 {self.module_name} 配置分析: {module_info}")
        return module_info
    
    def _prepare_module_variables(self, config: Dict[str, Any]):
        """
        准备模块特定的模板变量
        
        Args:
            config: 模块配置
        """
        try:
            # 添加模块特定的变量
            self.template_variables['module_name'] = self.module_name
            self.template_variables['module_description'] = config.get('description', f'{self.module_name} module')
            
            logger.info(f"Generic module {self.module_name} template variables prepared")
            
        except Exception as e:
            logger.error(f"准备通用模块 {self.module_name} 模板变量时出错: {e}")
            # 设置默认值
            self.template_variables['module_name'] = self.module_name
            self.template_variables['module_description'] = f'{self.module_name} module'
    
    def _process_generic_specific_logic(self, config: Dict[str, Any], output_dir: Path, result: ProcessResult):
        """
        处理通用模块特定的逻辑

        Args:
            config: 模块配置
            output_dir: 输出目录
            result: 处理结果
        """
        try:
            # 确保输出目录存在
            output_dir.mkdir(parents=True, exist_ok=True)

            # 根据模块类型进行特殊处理
            if self.module_name == 'uboot':
                # 为uboot脚本设置执行权限
                for script_file in output_dir.glob("*.sh"):
                    if script_file.exists():
                        script_file.chmod(0o755)
                        logger.info(f"Set U-Boot script execute permission: {script_file}")

            elif self.module_name == 'loader':
                # Special handling for loader directory
                logger.info(f"Loader directory created: {output_dir}")
                logger.info("Note: loader directory does not contain BUILD.gn file, please manually add required loader files")

            # Can add other module specific post-processing logic here

            logger.info(f"Generic module {self.module_name} processing completed")

        except Exception as e:
            result.add_warning(f"Error processing generic module {self.module_name} specific logic: {e}")

    def _load_generic_report_template(self, config: Dict[str, Any], language: str = 'zh') -> str:
        """
        加载通用模块的报告模板

        Args:
            config: 模块配置
            language: 语言版本 ('zh' 中文, 'en' 英文)

        Returns:
            渲染后的报告内容
        """
        try:
            # 确定模板目录
            template_dir_name = self.get_template_directory_name()
            if not template_dir_name:
                # 对于不需要模板的模块（如loader），使用默认报告
                return self._get_generic_default_report(config, language)

            template_dir = self.template_dir / "device" / "board" / template_dir_name

            # 根据语言选择模板文件
            if language == 'en':
                report_template_path = template_dir / "report_en.md.j2"
            else:
                report_template_path = template_dir / "report.md.j2"

            if not report_template_path.exists():
                logger.debug(f"通用模块报告模板不存在: {report_template_path}")
                return self._get_generic_default_report(config, language)

            # 准备报告专用的模板变量
            report_variables = self.template_variables.copy()
            report_variables.update({
                'module_config': config,
                'module_name': self.module_name,
                'module_type': 'standard' if self.module_name in self.STANDARD_MODULES else 'custom',
                'needs_build_gn': self.module_config.get('needs_build_gn', True),
                'description': config.get('description', self.module_config.get('description', f'{self.module_name} module'))
            })

            # 计算相对路径
            relative_path = report_template_path.relative_to(self.template_dir)

            # 渲染报告模板
            template = self.jinja_env.get_template(str(relative_path).replace('\\', '/'))
            report_content = template.render(**report_variables)

            logger.debug(f"成功加载通用模块报告模板: {report_template_path}")
            return report_content

        except Exception as e:
            logger.warning(f"加载通用模块报告模板失败: {e}")
            return self._get_generic_default_report(config, language)

    def _get_generic_default_report(self, config: Dict[str, Any], language: str = 'zh') -> str:
        """
        获取通用模块的默认报告内容

        Args:
            config: 模块配置
            language: 语言版本 ('zh' 中文, 'en' 英文)

        Returns:
            默认报告内容
        """
        module_type = 'standard' if self.module_name in self.STANDARD_MODULES else 'custom'
        description = config.get('description', self.module_config.get('description', f'{self.module_name} module'))
        needs_build_gn = self.module_config.get('needs_build_gn', True)

        if language == 'en':
            report_lines = [
                f"## {self.module_name.title()} Module",
                "",
                f"- **Module Type**: {module_type}",
                f"- **Description**: {description}",
                f"- **Build File**: {'BUILD.gn generated' if needs_build_gn else 'No BUILD.gn needed'}",
                "",
                "### Developer Tasks:",
                "1. **Check Generated Files**",
                "   - Verify all files are correctly generated",
                "   - Check configuration meets expectations",
                "",
                "2. **Complete Module Implementation**",
                "   - Add business logic according to specific requirements",
                "   - Test module functionality",
                "",
                "*Note: This module uses default report template, recommend creating custom report.md.j2 file*"
            ]
        else:
            report_lines = [
                f"## {self.module_name.title()} 模块",
                "",
                f"- **模块类型**: {module_type}",
                f"- **描述**: {description}",
                f"- **构建文件**: {'已生成 BUILD.gn' if needs_build_gn else '无需 BUILD.gn'}",
                "",
                "### 开发者需要完成的工作:",
                "1. **检查生成的文件**",
                "   - 验证所有文件已正确生成",
                "   - 检查配置是否符合预期",
                "",
                "2. **完成模块实现**",
                "   - 根据具体需求添加业务逻辑",
                "   - 测试模块功能",
                "",
                "*注意: 此模块使用默认报告模板，建议创建自定义的 report.md.j2 文件*"
            ]

        return "\n".join(report_lines)
