"""
Board processors module

This module contains processors for different board-level device types including:
- Audio processors for audio device code generation
- Camera processors for camera device code generation
- Kernel processors for kernel module code generation

The processors are organized by board device type to maintain clear separation
from SoC-specific processors and enable future expansion.
"""

from .audio_processor import AudioProcessor
from .camera_processor import CameraProcessor
from .kernel_processor import KernelProcessor
from .build_config_processor import BuildConfigProcessor
from .generic_processor import GenericProcessor

__all__ = [
    'AudioProcessor',
    'CameraProcessor',
    'KernelProcessor',
    'BuildConfigProcessor',
    'GenericProcessor'
]
