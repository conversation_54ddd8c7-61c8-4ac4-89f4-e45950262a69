"""
内核设备处理器

处理内核相关的代码生成，支持不同的内核模块和驱动。
这是一个示例处理器，展示如何添加新的设备类型处理器。
"""

from pathlib import Path
from typing import Dict, Any
import logging

from ..base_processor import BaseBoardProcessor, ProcessResult

logger = logging.getLogger(__name__)


class KernelProcessor(BaseBoardProcessor):
    """Kernel Board Device Processor"""

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether kernel device should be processed

        Args:
            config: Kernel device configuration

        Returns:
            True if kernel configuration exists, False otherwise
        """
        # Process as long as there is kernel configuration, no explicit enabled field needed
        return bool(config and (
            'linux_kernel_version' in config or
            'if_use_specific_kernel' in config or
            'if_use_kernel_patch' in config
        ))

    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name

        Args:
            config: Kernel device configuration

        Returns:
            Output directory name
        """
        # Kernel files are uniformly output to kernel directory
        # config parameter is retained to comply with base class interface, but not used here
        return 'kernel'

    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        return 'kernel'
    
    def process(self, config: Dict[str, Any], output_base_dir: Path) -> ProcessResult:
        """
        处理内核设备代码生成

        Args:
            config: 内核设备配置
            output_base_dir: 输出基础目录

        Returns:
            处理结果
        """
        result = ProcessResult()

        try:
            # Check if should process
            if not self.should_process(config):
                logger.info("Kernel device not enabled, skipping processing")
                result.success = True
                return result

            # Analyze kernel configuration
            kernel_info = self._analyze_kernel_config(config)
            logger.info(f"Kernel configuration: {kernel_info}")

            # Prepare kernel specific template variables
            self._prepare_kernel_variables(config, kernel_info)

            # Get output directory
            output_dir = output_base_dir / self.get_output_directory_name(config)
            template_dir = self.template_dir / "device" / "board" / self.get_template_directory_name()

            if not template_dir.exists():
                result.add_error(f"Kernel template directory does not exist: {template_dir}")
                return result

            logger.info(f"Processing kernel: {template_dir} -> {output_dir}")

            # Process template directory
            self._process_device_directory(template_dir, output_dir, result)

            # Perform special processing based on kernel type
            self._process_kernel_specific_logic(config, output_dir, result)

            result.success = len(result.errors) == 0
            return result

        except Exception as e:
            result.add_error(f"Error processing kernel device: {e}")
            return result
    
    def _analyze_kernel_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze kernel configuration

        Args:
            config: Kernel device configuration

        Returns:
            Analysis result

        Raises:
            ValueError: When input parameter format is incorrect
        """
        # Validate input parameter format
        self._validate_kernel_config(config)

        # Get version number and ensure string format to preserve precision
        raw_version = config.get('linux_kernel_version', '5.10')
        if isinstance(raw_version, (int, float)):
            # If it's a number, preserve original precision when converting to string
            # Special handling for version numbers like 5.10 to avoid floating point precision loss
            if isinstance(raw_version, float):
                if raw_version == 5.1:
                    # Special handling: if it's 5.1, it was likely originally 5.10
                    raw_version = "5.10"
                elif raw_version == 4.19:
                    # Handle 4.19 kernel version
                    raw_version = "4.19"
                elif raw_version == 6.6:
                    # Handle 6.6 kernel version
                    raw_version = "6.6"
                else:
                    # For other float values, format with appropriate precision
                    raw_version = f"{raw_version:.2f}".rstrip('0').rstrip('.')
            else:
                raw_version = str(raw_version)

        kernel_info = {
            'linux_kernel_version': raw_version,
            'if_use_specific_kernel': config.get('if_use_specific_kernel', True),
            'if_use_kernel_patch': config.get('if_use_kernel_patch', False),
            'patch_path': config.get('patch_path', {}),
            'enabled': True  # kernel configuration exists means enabled
        }

        logger.debug(f"Kernel configuration analysis: {kernel_info}")
        return kernel_info

    def _prepare_kernel_variables(self, config: Dict[str, Any], kernel_info: Dict[str, Any]):
        """
        Prepare kernel specific template variables

        Args:
            config: Kernel device configuration
            kernel_info: Analyzed kernel information
        """
        try:
            # Get version number from analyzed kernel_info, ensuring precision is fixed
            raw_version = kernel_info.get('linux_kernel_version', '5.10')

            # Convert to linux-X.X format
            linux_kernel_version = f"linux-{raw_version}"

            # Add to template variables
            self.template_variables['linux_kernel_version'] = linux_kernel_version
            self.template_variables['raw_kernel_version'] = raw_version

            # Process linux_kernel_source_path with variable substitution
            linux_kernel_source_path = config.get('linux_kernel_source_path', f"//kernel/linux/{linux_kernel_version}")

            # Handle ${linux_kernel_version} variable substitution in the path
            if '${linux_kernel_version}' in linux_kernel_source_path:
                linux_kernel_source_path = linux_kernel_source_path.replace('${linux_kernel_version}', linux_kernel_version)

            # Also handle ${raw_kernel_version} if present
            if '${raw_kernel_version}' in linux_kernel_source_path:
                linux_kernel_source_path = linux_kernel_source_path.replace('${raw_kernel_version}', raw_version)

            self.template_variables['linux_kernel_source_path'] = linux_kernel_source_path

            # 添加其他内核相关变量
            self.template_variables['if_use_specific_kernel'] = config.get('if_use_specific_kernel', True)
            self.template_variables['if_use_kernel_patch'] = config.get('if_use_kernel_patch', False)

            # 处理补丁路径
            patch_path = config.get('patch_path', {})
            kernel_patch_file = ''
            hdf_patch_file = ''

            if isinstance(patch_path, list) and len(patch_path) > 0:
                # 如果是列表格式，遍历所有项目找到对应的补丁
                for patch_item in patch_path:
                    if isinstance(patch_item, dict):
                        if 'kernel_patch' in patch_item:
                            kernel_patch_file = patch_item['kernel_patch']
                        if 'hdf_patch' in patch_item:
                            hdf_patch_file = patch_item['hdf_patch']
            elif isinstance(patch_path, dict):
                # 如果是字典格式
                kernel_patch_file = patch_path.get('kernel_patch', '')
                hdf_patch_file = patch_path.get('hdf_patch', '')

            # 设置完整的补丁文件路径
            self.template_variables['kernel_patch_path'] = kernel_patch_file
            self.template_variables['hdf_patch_path'] = hdf_patch_file

            # 提取补丁目录路径（去掉文件名和产品特定目录）
            import os

            def extract_patch_base_dir(patch_file_path):
                """
                从补丁文件路径中提取基础目录路径
                例如: kernel/linux/patches/linux-5.10/rk3568_patch/kernel.patch
                     -> kernel/linux/patches/linux-5.10
                """
                if not patch_file_path:
                    return ''

                # 获取目录路径（去掉文件名）
                dir_path = os.path.dirname(patch_file_path)

                # 分割路径为各个部分
                path_parts = dir_path.split('/')

                # 查找包含产品名称的目录部分，并去掉它及其后面的部分
                # 通常产品特定目录包含 _patch 后缀或者是产品名称
                filtered_parts = []
                for part in path_parts:
                    # 如果遇到包含 _patch 的目录或者是产品名称，则停止
                    if '_patch' in part or part == self.template_variables.get('product_name', ''):
                        break
                    filtered_parts.append(part)

                return '/'.join(filtered_parts) if filtered_parts else ''

            kernel_patch_dir = extract_patch_base_dir(kernel_patch_file)
            hdf_patch_dir = extract_patch_base_dir(hdf_patch_file)

            # Set patch directory path variables
            self.template_variables['kernel_patch_dir'] = kernel_patch_dir
            self.template_variables['hdf_patch_dir'] = hdf_patch_dir

            logger.info(f"Kernel template variables prepared: linux_kernel_version={linux_kernel_version}, raw_version={raw_version}")
            logger.info(f"Kernel source path: {linux_kernel_source_path}")
            logger.info(f"Kernel patch file: {kernel_patch_file}")
            logger.info(f"Kernel patch directory: {kernel_patch_dir}")
            logger.info(f"HDF patch file: {hdf_patch_file}")
            logger.info(f"HDF patch directory: {hdf_patch_dir}")
            logger.debug(f"Kernel related variables: {[(k, v) for k, v in self.template_variables.items() if 'kernel' in k.lower() or 'linux' in k.lower() or 'hdf' in k.lower() or 'patch' in k.lower()]}")

        except Exception as e:
            logger.error(f"Error preparing kernel template variables: {e}")
            # Set default values
            self.template_variables['linux_kernel_version'] = 'linux-5.10'
            self.template_variables['raw_kernel_version'] = '5.10'
    
    def _process_kernel_specific_logic(self, config: Dict[str, Any], output_dir: Path, result: ProcessResult):
        """
        Process kernel specific logic

        Args:
            config: Kernel device configuration
            output_dir: Output directory
            result: Processing result
        """
        try:
            linux_version = config.get('linux_kernel_version', '5.10')
            use_specific_kernel = config.get('if_use_specific_kernel', True)
            use_kernel_patch = config.get('if_use_kernel_patch', False)

            logger.info(f"Kernel version: {linux_version}")
            logger.info(f"Use specific kernel: {use_specific_kernel}")
            logger.info(f"Use kernel patch: {use_kernel_patch}")

            # Ensure output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)

            # Can add kernel specific post-processing logic here
            # For example: validate generated files, set file permissions, etc.

            # Check generated script files and set execute permissions
            for script_file in output_dir.glob("*.sh"):
                if script_file.exists():
                    script_file.chmod(0o755)
                    logger.info(f"Set script execute permission: {script_file}")

        except Exception as e:
            result.add_warning(f"Error processing kernel specific logic: {e}")

    def _validate_kernel_config(self, config: Dict[str, Any]):
        """
        Validate kernel configuration parameter format

        Args:
            config: Kernel device configuration

        Raises:
            ValueError: When input parameter format is incorrect
        """
        # Validate if linux_kernel_version is numeric type
        if 'linux_kernel_version' in config:
            version = config['linux_kernel_version']
            if not isinstance(version, (int, float, str)):
                raise ValueError(f"linux_kernel_version must be numeric type, current type: {type(version).__name__}")

            # If it's a string, check if it can be converted to a number
            if isinstance(version, str):
                try:
                    float(version)
                except ValueError:
                    raise ValueError(f"linux_kernel_version string must be valid numeric format, current value: '{version}'")

        # Validate if if_use_specific_kernel is boolean type
        if 'if_use_specific_kernel' in config:
            use_specific = config['if_use_specific_kernel']
            if not isinstance(use_specific, bool):
                raise ValueError(f"if_use_specific_kernel must be boolean type (true/false), current type: {type(use_specific).__name__}, current value: {use_specific}")

        # Validate if if_use_kernel_patch is boolean type
        if 'if_use_kernel_patch' in config:
            use_patch = config['if_use_kernel_patch']
            if not isinstance(use_patch, bool):
                raise ValueError(f"if_use_kernel_patch must be boolean type (true/false), current type: {type(use_patch).__name__}, current value: {use_patch}")

        logger.debug("Kernel configuration parameter format validation passed")
