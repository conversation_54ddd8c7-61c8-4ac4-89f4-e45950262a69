"""
Audio Device Processor

Handles audio-related code generation, supporting conditional generation for both ADM and ALSA frameworks.
Decides whether to generate audio_drivers directory or audio_alsa directory based on configuration.
"""

from pathlib import Path
from typing import Dict, Any
import logging

from ..base_processor import BaseBoardProcessor, ProcessResult

logger = logging.getLogger(__name__)


class AudioProcessor(BaseBoardProcessor):
    """Audio Board Device Processor"""

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether audio device should be processed

        Args:
            config: Audio device configuration

        Returns:
            True if audio device is enabled, False otherwise
        """
        return config.get('enabled', False)
    
    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name based on configuration

        Args:
            config: Audio device configuration

        Returns:
            Output directory name
        """
        # Check ADM framework
        adm_config = config.get('adm_drivers', {})
        if adm_config.get('enable', False):
            logger.info("Using ADM framework, output directory: audio_drivers")
            return 'audio_drivers'

        # Check ALSA framework
        alsa_config = config.get('alsa_drivers', {})
        if alsa_config.get('enable', False):
            logger.info("Using ALSA framework, output directory: audio_alsa")
            return 'audio_alsa'

        # Default to ADM framework
        logger.warning("Audio framework not explicitly specified, defaulting to ADM framework")
        return 'audio_drivers'

    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        return 'audio_drivers'

    def process(self, config: Dict[str, Any], output_base_dir: Path) -> ProcessResult:
        """
        Process audio device code generation

        Args:
            config: Audio device configuration
            output_base_dir: Output base directory

        Returns:
            Processing result
        """
        result = ProcessResult()
        
        try:
            # Check if should process
            if not self.should_process(config):
                logger.info("Audio device not enabled, skipping processing")
                result.success = True
                return result

            # Analyze configuration and determine processing strategy
            processing_strategy = self._analyze_audio_config(config)
            logger.info(f"Audio processing strategy: {processing_strategy}")

            # Process different frameworks based on strategy
            if processing_strategy['adm_enabled']:
                adm_result = self._process_adm_framework(config, output_base_dir)
                self._merge_results(result, adm_result)

            if processing_strategy['alsa_enabled']:
                alsa_result = self._process_alsa_framework(config, output_base_dir)
                self._merge_results(result, alsa_result)

            if not processing_strategy['adm_enabled'] and not processing_strategy['alsa_enabled']:
                result.add_warning("Audio device is enabled but no framework is enabled")

            # Generate module reports (Chinese and English versions)
            if len(result.errors) == 0:
                # Determine template directory - use actual audio template directory
                template_dir = self.template_dir / "device" / "board" / "audio" / "audio_drivers"

                # Generate Chinese report
                report_content_zh = self._load_report_template(template_dir, config, 'zh')
                result.set_module_report(self.get_output_directory_name(config), report_content_zh)

                # Generate English report
                report_content_en = self._load_report_template(template_dir, config, 'en')
                result.set_module_report_en(self.get_output_directory_name(config), report_content_en)

            result.success = len(result.errors) == 0
            return result

        except Exception as e:
            result.add_error(f"Error processing audio device: {e}")
            return result

    def _analyze_audio_config(self, config: Dict[str, Any]) -> Dict[str, bool]:
        """
        Analyze audio configuration and determine processing strategy

        Args:
            config: Audio device configuration

        Returns:
            Processing strategy dictionary

        Raises:
            ValueError: If configuration validation fails
        """
        adm_config = config.get('adm_drivers', {})
        alsa_config = config.get('alsa_drivers', {})

        # Validate configuration
        self._validate_audio_config(adm_config, alsa_config)

        strategy = {
            'adm_enabled': adm_config.get('enable', False),
            'alsa_enabled': alsa_config.get('enable', False),
            'adm_codec_chip': adm_config.get('codec_chip', ''),
            'alsa_codec_chip': alsa_config.get('codec_chip', ''),
        }

        logger.debug(f"Audio configuration analysis result: {strategy}")
        return strategy

    def _validate_audio_config(self, adm_config: Dict[str, Any], alsa_config: Dict[str, Any]) -> None:
        """
        Validate audio configuration

        Args:
            adm_config: ADM drivers configuration
            alsa_config: ALSA drivers configuration

        Raises:
            ValueError: If configuration validation fails
        """
        adm_enabled = adm_config.get('enable', False)
        alsa_enabled = alsa_config.get('enable', False)

        # Check if both ADM and ALSA are enabled
        if adm_enabled and alsa_enabled:
            raise ValueError("ADM drivers and ALSA drivers cannot be enabled simultaneously. "
                           "Please enable only one audio framework.")

        # Validate bus_type for ADM drivers
        if adm_enabled:
            bus_type = adm_config.get('bus_type', '')
            if bus_type and bus_type not in ['platform', 'i2c']:
                raise ValueError(f"Invalid bus_type '{bus_type}' for ADM drivers. "
                               "Valid values are: 'platform', 'i2c'")

        logger.debug("Audio configuration validation passed")
    
    def _process_adm_framework(self, config: Dict[str, Any], output_base_dir: Path) -> ProcessResult:
        """
        处理ADM框架

        Args:
            config: 音频设备配置
            output_base_dir: 输出基础目录

        Returns:
            处理结果
        """
        result = ProcessResult()

        try:
            # ADM框架输出到audio_drivers目录
            output_dir = output_base_dir / 'audio_drivers'
            # ADM框架使用audio/audio_drivers模板目录
            template_dir = self.template_dir / "device" / "board" / "audio" / "audio_drivers"

            logger.info(f"Processing ADM framework: {template_dir} -> {output_dir}")

            # 处理模板目录，使用自定义的处理逻辑来处理codec目录结构
            self._process_audio_device_directory(template_dir, output_dir, result, config)

            return result

        except Exception as e:
            result.add_error(f"处理ADM框架时出错: {e}")
            return result
    
    def _process_alsa_framework(self, config: Dict[str, Any], output_base_dir: Path) -> ProcessResult:
        """
        处理ALSA框架

        Args:
            config: 音频设备配置
            output_base_dir: 输出基础目录

        Returns:
            处理结果
        """
        result = ProcessResult()

        try:
            # ALSA框架输出到audio_alsa目录
            output_dir = output_base_dir / 'audio_alsa'

            # 获取ALSA配置
            alsa_config = config.get('alsa_drivers', {})
            implementation_method = alsa_config.get('implementation_method', '')

            logger.info(f"Processing ALSA framework: implementation_method={implementation_method}")

            # If framework implementation method, directly execute copy_file_alsa.sh script
            if implementation_method == 'framework':
                logger.info("ALSA framework using framework implementation method, executing copy_file_alsa.sh script")
                self._execute_alsa_framework_script(output_dir, result)
            else:
                # Other implementation methods (manual) also execute ALSA script, not using audio_drivers template
                logger.info("ALSA framework using manual implementation method, executing copy_file_alsa.sh script")
                self._execute_alsa_framework_script(output_dir, result)

            return result

        except Exception as e:
            result.add_error(f"处理ALSA框架时出错: {e}")
            return result
    
    def _merge_results(self, main_result: ProcessResult, sub_result: ProcessResult):
        """
        合并处理结果
        
        Args:
            main_result: 主结果
            sub_result: 子结果
        """
        main_result.generated_files.extend(sub_result.generated_files)
        main_result.executed_scripts.extend(sub_result.executed_scripts)
        main_result.errors.extend(sub_result.errors)
        main_result.warnings.extend(sub_result.warnings)
        main_result.output_directories.extend(sub_result.output_directories)

    def _process_audio_device_directory(self, template_dir: Path, output_dir: Path, result: ProcessResult, config: Dict[str, Any]):
        """
        处理音频设备目录，特殊处理codec目录结构

        Args:
            template_dir: 模板目录
            output_dir: 输出目录
            result: 结果累积器
            config: 音频配置
        """
        try:
            # 1. 首先检查并执行copy_file.sh脚本
            copy_script = template_dir / "copy_file.sh"
            if copy_script.exists() and copy_script.stat().st_size > 0:
                # 传递项目根目录参数
                project_root = self.template_dir.parent  # 从template目录向上一级到项目根目录
                self._execute_script(copy_script, result, project_root)

            # 2. 处理当前目录中的模板文件
            for item in template_dir.iterdir():
                if item.is_file() and item.suffix == '.j2':
                    # 跳过报告模板文件，这些文件会在后续单独处理
                    if item.name in ['report.md.j2', 'report_en.md.j2']:
                        continue
                    self._process_template_file(item, output_dir, result)
                elif item.is_dir():
                    # 特殊处理codec目录
                    if item.name == 'codec':
                        self._process_codec_directory(item, output_dir, result, config)
                    else:
                        # 递归处理其他子目录
                        sub_output_dir = output_dir / item.name
                        self._process_audio_device_directory(item, sub_output_dir, result, config)

        except Exception as e:
            result.add_error(f"处理音频设备目录 {template_dir} 时出错: {e}")

    def _process_codec_directory(self, codec_template_dir: Path, output_dir: Path, result: ProcessResult, config: Dict[str, Any]):
        """
        处理codec目录，创建芯片特定的子目录

        Args:
            codec_template_dir: codec模板目录
            output_dir: 输出目录
            result: 结果累积器
            config: 音频配置
        """
        try:
            # 获取codec芯片名称
            adm_config = config.get('adm_drivers', {})
            alsa_config = config.get('alsa_drivers', {})

            codec_chip = ''
            if adm_config.get('enable', False):
                codec_chip = adm_config.get('codec_chip', '')
            elif alsa_config.get('enable', False):
                codec_chip = alsa_config.get('codec_chip', '')

            if not codec_chip:
                result.add_warning("未找到codec芯片名称，使用默认目录结构")
                # 使用原始的处理方式
                sub_output_dir = output_dir / 'codec'
                self._process_device_directory(codec_template_dir, sub_output_dir, result)
                return

            # 创建芯片特定的codec目录：codec/{codec_chip}_codec/
            codec_chip_dir = f"{codec_chip}_codec"
            codec_output_dir = output_dir / 'codec' / codec_chip_dir

            logger.info(f"Processing codec directory: {codec_template_dir} -> {codec_output_dir}")

            # 处理codec模板目录
            self._process_device_directory(codec_template_dir, codec_output_dir, result)

        except Exception as e:
            result.add_error(f"处理codec目录时出错: {e}")

    def _execute_alsa_framework_script(self, output_dir: Path, result: ProcessResult):
        """
        执行ALSA框架的copy_file_alsa.sh脚本

        Args:
            output_dir: 输出目录
            result: 结果累积器
        """
        try:
            # 查找copy_file_alsa.sh脚本
            # ALSA脚本在template/device/board/audio/audio_alsa/目录下
            script_path = self.template_dir / "device" / "board" / "audio" / "audio_alsa" / "copy_file_alsa.sh"


            if not script_path.exists():
                result.add_error(f"未找到copy_file_alsa.sh脚本，尝试的路径: {script_path}")
                return

            logger.info(f"Executing ALSA framework script: {script_path}")

            # 确保输出目录存在
            output_dir.mkdir(parents=True, exist_ok=True)

            # 执行脚本，传递项目根目录参数
            project_root = self.template_dir.parent  # 从template目录向上一级到项目根目录
            self._execute_script(script_path, result, project_root)

        except Exception as e:
            result.add_error(f"执行ALSA框架脚本时出错: {e}")
