"""
OMX IL SoC Processor

Handles OpenMAX IL code generation for SoC-level components.
"""

import logging
from pathlib import Path
from typing import Dict, Any

from ..base_processor import BaseBoardProcessor

logger = logging.getLogger(__name__)


class OMXILProcessor(BaseBoardProcessor):
    """OMX IL SoC processor"""

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether OMX IL component should be processed

        Args:
            config: OMX IL configuration

        Returns:
            True if OMX IL is enabled, False otherwise
        """
        return config.get("enabled", True)  # Default enabled for SoC components
    
    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name
        
        Args:
            config: OMX IL configuration
            
        Returns:
            Output directory name
        """
        return "omx_il"
    
    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        return "omx_il"
