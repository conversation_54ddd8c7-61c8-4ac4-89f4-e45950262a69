"""
Codec SoC Processor

Handles codec code generation for SoC-level components.
"""

import logging
from pathlib import Path
from typing import Dict, Any

from ..base_processor import BaseBoardProcessor

logger = logging.getLogger(__name__)


class CodecProcessor(BaseBoardProcessor):
    """Codec SoC processor"""

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether codec component should be processed

        Args:
            config: Codec configuration

        Returns:
            True if codec is enabled, False otherwise
        """
        return config.get("enabled", True)  # Default enabled for SoC components
    
    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name
        
        Args:
            config: Codec configuration
            
        Returns:
            Output directory name
        """
        return "codec"
    
    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        return "codec"
