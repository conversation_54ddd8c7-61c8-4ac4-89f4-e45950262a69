"""
SoC Build Config Processor

Handles SoC build configuration file generation.
"""

import logging
from pathlib import Path
from typing import Dict, Any

from ..base_processor import BaseBoardProcessor

logger = logging.getLogger(__name__)


class SoCBuildConfigProcessor(BaseBoardProcessor):
    """SoC build configuration processor"""

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether build config should be processed

        Args:
            config: Build config configuration

        Returns:
            True if build config is enabled, False otherwise
        """
        return config.get("enabled", True)  # Default enabled for SoC components
    
    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name - for build config, output to root SoC directory
        
        Args:
            config: Build config configuration
            
        Returns:
            Empty string to output to root SoC directory
        """
        return ""  # Output to root SoC directory
    
    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        return "build_config"
