"""
GPU SoC Processor

Handles GPU code generation for SoC-level components.
"""

import logging
from pathlib import Path
from typing import Dict, Any

from ..base_processor import BaseBoardProcessor

logger = logging.getLogger(__name__)


class GPUProcessor(BaseBoardProcessor):
    """GPU SoC processor"""

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether GPU component should be processed

        Args:
            config: GPU configuration

        Returns:
            True if GPU is enabled, False otherwise
        """
        return config.get("enabled", True)  # Default enabled for SoC components
    
    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name
        
        Args:
            config: GPU configuration
            
        Returns:
            Output directory name
        """
        return "gpu"
    
    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        return "gpu"
