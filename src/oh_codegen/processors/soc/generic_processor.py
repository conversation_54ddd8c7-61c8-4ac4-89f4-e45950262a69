"""
Generic SoC Processor

Handles generic SoC component code generation, including custom modules.
"""

import logging
from pathlib import Path
from typing import Dict, Any

from ..base_processor import BaseBoardProcessor

logger = logging.getLogger(__name__)


class SoCGenericProcessor(BaseBoardProcessor):
    """Generic SoC processor for custom modules"""

    def __init__(self, template_dir: Path, jinja_env, template_variables: Dict[str, Any], module_name: str):
        """
        Initialize generic SoC processor

        Args:
            template_dir: Template root directory
            jinja_env: Jinja2 environment
            template_variables: Template variables dictionary
            module_name: Module name
        """
        super().__init__(template_dir, jinja_env, template_variables)
        self.module_name = module_name
        logger.info(f"Initialize generic SoC processor: {module_name}")

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether generic SoC component should be processed

        Args:
            config: Component configuration

        Returns:
            True if component is enabled, False otherwise
        """
        return config.get("enabled", True)  # Default enabled for SoC components
    
    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name
        
        Args:
            config: Component configuration
            
        Returns:
            Output directory name
        """
        return self.module_name
    
    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        # For generic modules, use my_group template
        return "my_group"
