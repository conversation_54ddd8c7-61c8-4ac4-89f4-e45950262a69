"""
Processor Module

Provides independent processors for different level components:
- Board processors: Handle board-level devices (audio, camera, kernel, etc.)
- SoC processors: Handle chip-level components (display, gpu, memory, etc.)

Each processor is responsible for handling code generation logic for specific component types.
"""

from .base_processor import BaseBoardProcessor
from .board import (
    AudioProcessor,
    CameraProcessor,
    KernelProcessor,
    BuildConfigProcessor,
    GenericProcessor
)

# Board processor registry
BOARD_PROCESSORS = {
    'audio_drivers': AudioProcessor,
    'camera_drivers': CameraProcessor,
    'kernel_drivers': KernelProcessor,
    'kernel': KernelProcessor,  # Support kernel configuration name
    'build_config': BuildConfigProcessor,
    # Can continue adding other Board processors
    # 'display_drivers': DisplayProcessor,
}

# Reserved registry for future SoC processors
# SOC_PROCESSORS = {
#     'display_controller': DisplayControllerProcessor,
#     'gpu': GPUProcessor,
#     'memory_controller': MemoryControllerProcessor,
# }

def get_processor(device_type: str) -> BaseBoardProcessor:
    """
    Get processor for specified device type

    Args:
        device_type: Device type name

    Returns:
        Corresponding processor class

    Raises:
        ValueError: If device type is not supported
    """
    if device_type not in BOARD_PROCESSORS:
        raise ValueError(f"Unsupported device type: {device_type}")

    return BOARD_PROCESSORS[device_type]

def list_supported_devices():
    """Get list of supported device types"""
    return list(BOARD_PROCESSORS.keys())

__all__ = [
    'BaseBoardProcessor',
    'AudioProcessor',
    'CameraProcessor',
    'KernelProcessor',
    'BuildConfigProcessor',
    'GenericProcessor',
    'BOARD_PROCESSORS',
    'get_processor',
    'list_supported_devices'
]
