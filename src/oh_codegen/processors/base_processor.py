"""
Device Processor Base Class

Defines common interfaces and basic functionality for all device processors.
"""

import os
import subprocess
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class ProcessResult:
    """Processing result"""

    def __init__(self):
        self.success = False
        self.generated_files = []
        self.executed_scripts = []
        self.errors = []
        self.warnings = []
        self.output_directories = []
        self.module_report = ""     # Chinese module report content
        self.module_report_en = ""  # English module report content
        self.module_name = ""       # Module name

    def add_error(self, error: str):
        self.errors.append(error)
        logger.error(error)

    def add_warning(self, warning: str):
        self.warnings.append(warning)
        logger.warning(warning)

    def add_generated_file(self, file_path: str):
        self.generated_files.append(file_path)
        logger.info(f"Generated file: {file_path}")

    def add_executed_script(self, script_path: str):
        self.executed_scripts.append(script_path)
        logger.info(f"Executed script: {script_path}")

    def set_module_report(self, module_name: str, report_content: str):
        """Set Chinese module report"""
        self.module_name = module_name
        self.module_report = report_content

    def set_module_report_en(self, module_name: str, report_content: str):
        """Set English module report"""
        self.module_name = module_name
        self.module_report_en = report_content


class BaseBoardProcessor(ABC):
    """Board device processor base class"""

    def __init__(self, template_dir: Path, jinja_env, template_variables: Dict[str, Any]):
        """
        Initialize processor

        Args:
            template_dir: Template root directory
            jinja_env: Jinja2 environment
            template_variables: Template variables dictionary
        """
        self.template_dir = template_dir
        self.jinja_env = jinja_env
        self.template_variables = template_variables
        self.device_type = self.__class__.__name__.replace('Processor', '').lower()

        logger.info(f"Initialize {self.device_type} processor")

    @abstractmethod
    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether this device type should be processed

        Args:
            config: Device configuration

        Returns:
            True if should be processed, False otherwise
        """
        pass

    @abstractmethod
    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name

        Args:
            config: Device configuration

        Returns:
            Output directory name
        """
        pass
    
    @abstractmethod
    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        pass

    def get_template_base_path(self) -> str:
        """
        Get template base path (board, soc, or vendor) based on processor module location

        Returns:
            Template base path
        """
        # 自动检测处理器类型：根据模块路径确定模板基础路径
        module_path = self.__class__.__module__
        if ".soc." in module_path:
            return "soc"
        elif ".vendor." in module_path:
            return "vendor"
        else:
            return "board"

    def process(self, config: Dict[str, Any], output_base_dir: Path) -> ProcessResult:
        """
        Process device code generation

        Args:
            config: Device configuration
            output_base_dir: Output base directory

        Returns:
            Processing result
        """
        result = ProcessResult()

        try:
            # Check if should process
            if not self.should_process(config):
                logger.info(f"{self.device_type} processor: Device not enabled, skipping processing")
                result.success = True
                return result

            # Get output directory
            output_dir_name = self.get_output_directory_name(config)
            output_dir = output_base_dir / output_dir_name

            # Get template directory
            template_dir_name = self.get_template_directory_name()
            template_base_path = self.get_template_base_path()

            # Vendor templates are in template/vendor/, others are in template/device/
            if template_base_path == "vendor":
                device_template_dir = self.template_dir / "vendor" / template_dir_name
            else:
                device_template_dir = self.template_dir / "device" / template_base_path / template_dir_name

            if not device_template_dir.exists():
                result.add_error(f"Template directory does not exist: {device_template_dir}")
                return result

            logger.info(f"Processing {self.device_type}: {template_dir_name} -> {output_dir_name}")

            # Execute specific processing logic
            self._process_device_directory(device_template_dir, output_dir, result)

            # Generate module report (Chinese and English versions)
            if len(result.errors) == 0:
                # Generate Chinese report
                report_content_zh = self._load_report_template(device_template_dir, config, 'zh')
                result.set_module_report(output_dir_name, report_content_zh)

                # Generate English report
                report_content_en = self._load_report_template(device_template_dir, config, 'en')
                result.set_module_report_en(output_dir_name, report_content_en)

            result.success = len(result.errors) == 0
            return result

        except Exception as e:
            result.add_error(f"Error processing {self.device_type}: {e}")
            return result

    def _process_device_directory(self, template_dir: Path, output_dir: Path, result: ProcessResult):
        """
        Recursively process device directory

        Args:
            template_dir: Template directory
            output_dir: Output directory
            result: Result accumulator
        """
        try:
            # 1. First check and execute copy_file.sh script
            copy_script = template_dir / "copy_file.sh"
            if copy_script.exists() and copy_script.stat().st_size > 0:
                # Pass project root directory parameter
                project_root = self.template_dir.parent  # Go up one level from template directory to project root
                self._execute_script(copy_script, result, project_root)

            # 2. Process template files in current directory
            for item in template_dir.iterdir():
                if item.is_file() and item.suffix == '.j2':
                    # Skip report template files, these will be processed separately later
                    if item.name in ['report.md.j2', 'report_en.md.j2']:
                        continue
                    self._process_template_file(item, output_dir, result)
                elif item.is_dir():
                    # Recursively process subdirectories
                    sub_output_dir = output_dir / item.name
                    self._process_device_directory(item, sub_output_dir, result)

        except Exception as e:
            result.add_error(f"Error processing directory {template_dir}: {e}")

    def _execute_script(self, script_path: Path, result: ProcessResult, project_root: Path = None):
        """
        Execute script file

        Args:
            script_path: Script file path
            result: Result accumulator
            project_root: Project root directory path, if provided will be passed as first argument to script
        """
        try:
            logger.info(f"Execute script: {script_path}")

            # Prepare environment variables
            env = os.environ.copy()
            for key, value in self.template_variables.items():
                if value:
                    env[f"TEMPLATE_{key.upper()}"] = str(value)

            # Build command arguments
            cmd_args = ['bash', str(script_path)]
            if project_root:
                cmd_args.append(str(project_root))
                logger.info(f"Pass project root directory parameter: {project_root}")

            # Execute script
            process = subprocess.run(
                cmd_args,
                cwd=script_path.parent,
                env=env,
                capture_output=True,
                text=True,
                timeout=60
            )

            if process.returncode == 0:
                result.add_executed_script(str(script_path))
                # 记录脚本输出用于调试
                if process.stdout:
                    logger.debug(f"Script output: {process.stdout}")
            else:
                result.add_error(f"Script execution failed: {script_path}, return code: {process.returncode}, error: {process.stderr}")
                # 记录脚本输出用于调试
                if process.stdout:
                    logger.debug(f"Script stdout: {process.stdout}")
                if process.stderr:
                    logger.debug(f"Script stderr: {process.stderr}")

        except Exception as e:
            result.add_error(f"Error executing script {script_path}: {e}")

    def _process_template_file(self, template_path: Path, output_dir: Path, result: ProcessResult):
        """Process single template file"""
        try:
            # Calculate relative path and output filename
            relative_path = template_path.relative_to(self.template_dir)
            output_filename = template_path.stem  # Remove .j2 suffix

            # Process template variables in filename
            output_filename = self._resolve_template_variables_in_string(output_filename)
            output_file_path = output_dir / output_filename
            
            # Ensure output directory exists
            output_file_path.parent.mkdir(parents=True, exist_ok=True)

            # Render template
            template = self.jinja_env.get_template(str(relative_path).replace('\\', '/'))
            rendered_content = template.render(**self.template_variables)

            # Write file
            with open(output_file_path, 'w', encoding='utf-8') as f:
                f.write(rendered_content)

            result.add_generated_file(str(output_file_path))

        except Exception as e:
            result.add_error(f"Error processing template file {template_path}: {e}")

    def _resolve_template_variables_in_string(self, text: str) -> str:
        """Resolve template variables in string"""
        try:
            template = self.jinja_env.from_string(text)
            return template.render(**self.template_variables)
        except Exception as e:
            logger.warning(f"Failed to resolve template variables, using original string: {text}, error: {e}")
            return text

    def _load_report_template(self, template_dir: Path, config: Dict[str, Any], language: str = 'zh') -> str:
        """
        Load and merge report content from markdown files

        Args:
            template_dir: Template directory
            config: Module configuration
            language: Language version ('zh' Chinese, 'en' English)

        Returns:
            Merged report content
        """
        try:
            # Collect all report.md files from the template directory and its subdirectories
            report_contents = []

            # Select report file name based on language
            if language == 'en':
                report_filename = "report_en.md"
            else:
                report_filename = "report.md"

            # Recursively find all report.md files
            report_files = self._find_report_files(template_dir, report_filename)

            if not report_files:
                logger.debug(f"No {report_filename} files found in: {template_dir}")
                return self._get_default_report(config, language)

            # Prepare report-specific template variables
            report_variables = self.template_variables.copy()
            report_variables.update({
                'module_config': config,
                'module_name': getattr(self, 'module_name', self.device_type),
                'device_type': self.device_type
            })

            # Read and merge content from all report files
            for report_file in sorted(report_files):  # Sort for consistent order
                try:
                    with open(report_file, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            # Check if content contains Jinja2 template syntax
                            if self._contains_template_syntax(content):
                                # Render as template
                                try:
                                    relative_path = report_file.relative_to(self.template_dir)
                                    template = self.jinja_env.get_template(str(relative_path).replace('\\', '/'))
                                    rendered_content = template.render(**report_variables)
                                    content = rendered_content.strip()
                                    logger.debug(f"Rendered template report file: {report_file}")
                                except Exception as e:
                                    logger.error(f"Failed to render template {report_file}: {e}")
                                    logger.error(f"Template variables: {report_variables}")
                                    # Fall back to raw content

                            # Add relative path as a comment for debugging
                            relative_path = report_file.relative_to(template_dir)
                            report_contents.append(f"<!-- Content from: {relative_path} -->")
                            report_contents.append(content)
                            report_contents.append("")  # Add empty line between sections

                    logger.debug(f"Successfully loaded report file: {report_file}")

                except Exception as e:
                    logger.warning(f"Failed to read report file {report_file}: {e}")
                    continue

            if report_contents:
                merged_content = "\n".join(report_contents).strip()
                logger.info(f"Successfully merged {len(report_files)} report files from {template_dir}")
                return merged_content
            else:
                logger.debug(f"No valid content found in report files")
                return self._get_default_report(config, language)

        except Exception as e:
            logger.warning(f"Failed to load report files: {e}")
            return self._get_default_report(config, language)

    def _find_report_files(self, template_dir: Path, report_filename: str) -> List[Path]:
        """
        Recursively find all report files in the template directory

        Args:
            template_dir: Template directory to search
            report_filename: Name of report file to find (e.g., 'report.md', 'report_en.md')

        Returns:
            List of found report file paths
        """
        report_files = []

        try:
            # Search in current directory
            report_file = template_dir / report_filename
            if report_file.exists() and report_file.is_file():
                report_files.append(report_file)

            # Recursively search in subdirectories
            for item in template_dir.iterdir():
                if item.is_dir():
                    # Skip certain directories that shouldn't contain reports
                    if item.name.startswith('.') or item.name in ['__pycache__', 'node_modules']:
                        continue

                    # Recursively search subdirectories
                    sub_reports = self._find_report_files(item, report_filename)
                    report_files.extend(sub_reports)

        except Exception as e:
            logger.warning(f"Error searching for report files in {template_dir}: {e}")

        return report_files

    def _contains_template_syntax(self, content: str) -> bool:
        """
        Check if content contains Jinja2 template syntax

        Args:
            content: Content to check

        Returns:
            True if content contains template syntax, False otherwise
        """
        # Check for common Jinja2 syntax patterns
        template_patterns = [
            '{{',  # Variable substitution
            '{%',  # Control structures
            '{#',  # Comments
        ]

        return any(pattern in content for pattern in template_patterns)

    def _get_default_report(self, config: Dict[str, Any], language: str = 'zh') -> str:
        """
        Get default report content

        Args:
            config: Module configuration
            language: Language version ('zh' Chinese, 'en' English)

        Returns:
            Default report content
        """
        module_name = getattr(self, 'module_name', self.device_type)

        if language == 'en':
            return f"""## {module_name.title()} Module

### Developer Tasks:
1. **Check Generated Files**
   - Verify all files are correctly generated
   - Check configuration meets expectations

2. **Complete Module Implementation**
   - Add business logic according to specific requirements
   - Test module functionality

*Note: This module uses default report template, recommend creating custom report.md.j2 file*
"""
        else:
            return f"""## {module_name.title()} 模块

### 开发者需要完成的工作:
1. **检查生成的文件**
   - 验证所有文件已正确生成
   - 检查配置是否符合预期

2. **完成模块实现**
   - 根据具体需求添加业务逻辑
   - 测试模块功能

*注意: 此模块使用默认报告模板，建议创建自定义的 report.md.j2 文件*
"""
