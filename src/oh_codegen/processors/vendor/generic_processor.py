"""
Generic Vendor Processor

Handles generic vendor component code generation, including various configuration components.
"""

import logging
from pathlib import Path
from typing import Dict, Any

from ..base_processor import BaseBoardProcessor

logger = logging.getLogger(__name__)


class VendorGenericProcessor(BaseBoardProcessor):
    """Generic vendor processor for various vendor components"""

    def __init__(self, template_dir: Path, jinja_env, template_variables: Dict[str, Any], component_name: str):
        """
        Initialize generic vendor processor

        Args:
            template_dir: Template root directory
            jinja_env: Jinja2 environment
            template_variables: Template variables dictionary
            component_name: Component name
        """
        super().__init__(template_dir, jinja_env, template_variables)
        self.component_name = component_name
        logger.info(f"Initialize generic vendor processor: {component_name}")

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether vendor component should be processed

        Args:
            config: Component configuration

        Returns:
            True if component is enabled, False otherwise
        """
        return config.get("enabled", True)  # Default enabled for vendor components
    
    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name
        
        Args:
            config: Component configuration
            
        Returns:
            Output directory name
        """
        return self.component_name
    
    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        return self.component_name
