"""
Vendor Processor Module

Provides processors for vendor-level components:
- Generic vendor processor: Handle various vendor components with similar operations
- Build config processor: Handle vendor build configuration
- Config processors: Handle various configuration components

Each processor is responsible for handling code generation logic for specific vendor component types.
"""

from .generic_processor import VendorGenericProcessor
from .build_config_processor import VendorBuildConfigProcessor

# Vendor processor registry - 将类似的组件合并到同一个处理器
VENDOR_PROCESSORS = {
    # 配置类组件 - 使用通用处理器
    'bluetooth': VendorGenericProcessor,
    'default_app_config': VendorGenericProcessor,
    'etc': VendorGenericProcessor,
    'hals': VendorGenericProcessor,
    'image_conf': VendorGenericProcessor,
    'preinstall-config': VendorGenericProcessor,
    'resourceschedule': VendorGenericProcessor,
    'security_config': VendorGenericProcessor,
    'updater_config': VendorGenericProcessor,
    'window_config': VendorGenericProcessor,
    'hdf_config': VendorGenericProcessor,
    
    # 构建配置 - 使用专门的构建配置处理器
    'build_config': VendorBuildConfigProcessor,
}

def get_vendor_processor(component_type: str):
    """
    Get processor for specified vendor component type

    Args:
        component_type: Vendor component type name

    Returns:
        Corresponding processor class

    Raises:
        ValueError: If component type is not supported
    """
    if component_type not in VENDOR_PROCESSORS:
        # 对于未知组件，使用通用处理器
        return VendorGenericProcessor

    return VENDOR_PROCESSORS[component_type]

def list_supported_vendor_components():
    """Get list of supported vendor component types"""
    return list(VENDOR_PROCESSORS.keys())

__all__ = [
    'VendorGenericProcessor',
    'VendorBuildConfigProcessor',
    'get_vendor_processor',
    'list_supported_vendor_components',
    'VENDOR_PROCESSORS'
]
