"""
简化代码生成器使用示例

展示如何使用新的简化代码生成器
"""

from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from oh_codegen.simplified_generator import SimpleCodeGenerator, generate_device_code


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 方式1: 使用便捷函数
    result = generate_device_code(
        config_file="orangepi_5b.yaml",
        clean_output=True
    )
    
    print(f"生成结果: {'成功' if result['success'] else '失败'}")
    print(f"生成文件数: {len(result['generated_files'])}")
    print(f"执行脚本数: {len(result['executed_scripts'])}")
    
    if result['errors']:
        print("错误:")
        for error in result['errors']:
            print(f"  - {error}")


def example_advanced_usage():
    """高级使用示例"""
    print("\n=== 高级使用示例 ===")
    
    # 方式2: 使用生成器类
    generator = SimpleCodeGenerator()
    
    # 加载配置
    config_data = generator.load_config("orangepi_5b.yaml")
    print(f"产品名称: {config_data.get('product_name')}")
    
    # 查看启用的模块
    enabled_modules = generator.get_enabled_modules()
    print(f"启用的模块: {enabled_modules}")
    
    # 查看模板变量
    print("模板变量:")
    for key, value in generator.template_variables.items():
        print(f"  {key}: {value}")
    
    # 执行生成
    result = generator.generate_code("orangepi_5b.yaml", clean_output=False)
    
    print(f"\n生成结果: {'成功' if result['success'] else '失败'}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    try:
        # 尝试加载不存在的配置文件
        result = generate_device_code("nonexistent.yaml")
    except FileNotFoundError as e:
        print(f"配置文件不存在: {e}")
    except Exception as e:
        print(f"其他错误: {e}")


def example_custom_project_root():
    """自定义项目根目录示例"""
    print("\n=== 自定义项目根目录示例 ===")
    
    # 指定项目根目录
    custom_root = Path("/path/to/your/project")  # 替换为实际路径
    
    try:
        generator = SimpleCodeGenerator(project_root=custom_root)
        # ... 其他操作
        print("使用自定义项目根目录成功")
    except Exception as e:
        print(f"使用自定义项目根目录失败: {e}")


if __name__ == "__main__":
    # 设置日志
    import logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # 运行示例
    example_basic_usage()
    example_advanced_usage()
    example_error_handling()
    example_custom_project_root()
    
    print("\n所有示例运行完成！")
