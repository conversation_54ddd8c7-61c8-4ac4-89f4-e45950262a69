#!/usr/bin/env python3
"""
基本使用示例

演示如何使用设备代码生成器API进行代码生成。
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from oh_codegen import create_generator, ConfigParser


def basic_generation_example():
    """基本代码生成示例"""
    print("=== 基本代码生成示例 ===")
    
    try:
        # 创建代码生成器
        generator = create_generator()
        
        # 设置生成选项
        generator.set_option('clean_output', True)  # 清理输出目录
        generator.set_option('validate_templates', True)  # 验证模板
        
        # 执行代码生成
        result = generator.generate()
        
        if result.success:
            print(f"✅ 代码生成成功!")
            print(f"   产品名称: {result.product_name}")
            print(f"   输出目录: {result.output_directory}")
            print(f"   生成文件: {len(result.generated_files)} 个")
            print(f"   执行脚本: {len(result.executed_scripts)} 个")
            print(f"   生成耗时: {result.generation_time:.2f} 秒")
            
            # 显示生成的文件列表
            if result.generated_files:
                print("\n生成的文件:")
                for i, file in enumerate(result.generated_files[:10]):  # 最多显示10个
                    rel_path = file.output_path.relative_to(result.output_directory)
                    print(f"   {i+1:2d}. {rel_path} ({file.size} bytes)")
                
                if len(result.generated_files) > 10:
                    print(f"   ... 还有 {len(result.generated_files) - 10} 个文件")
            
            # 显示警告信息
            if result.warnings:
                print(f"\n⚠️  警告信息 ({len(result.warnings)} 个):")
                for warning in result.warnings:
                    print(f"   - {warning}")
            
        else:
            print(f"❌ 代码生成失败!")
            print(f"   错误数量: {len(result.errors)}")
            for error in result.errors:
                print(f"   - {error}")
            
            return False
            
    except Exception as e:
        print(f"❌ 生成过程中发生异常: {e}")
        return False
    
    return True


def preview_generation_example():
    """预览生成内容示例"""
    print("\n=== 预览生成内容示例 ===")
    
    try:
        # 创建代码生成器
        generator = create_generator()
        
        # 预览生成内容
        preview_info = generator.preview_generation()
        
        if 'error' in preview_info:
            print(f"❌ 预览失败: {preview_info['error']}")
            return False
        
        print(f"✅ 预览成功!")
        print(f"   产品名称: {preview_info['product_name']}")
        print(f"   启用模块: {', '.join(preview_info['enabled_modules'])}")
        print(f"   模板文件: {preview_info['total_templates']} 个")
        print(f"   脚本文件: {preview_info['total_scripts']} 个")
        
        # 显示各模块详情
        print("\n各模块详情:")
        for module, template_count in preview_info['templates_by_module'].items():
            script_count = preview_info['scripts_by_module'].get(module, 0)
            print(f"   {module}: {template_count} 个模板, {script_count} 个脚本")
        
        # 显示部分模板变量
        print("\n关键模板变量:")
        key_vars = ['product_name', 'soc', 'codec_chip', 'soc_company', 'device_company']
        for key in key_vars:
            if key in preview_info['variables']:
                print(f"   {key}: {preview_info['variables'][key]}")
        
    except Exception as e:
        print(f"❌ 预览过程中发生异常: {e}")
        return False
    
    return True


def dry_run_example():
    """试运行示例"""
    print("\n=== 试运行示例 ===")
    
    try:
        # 创建代码生成器
        generator = create_generator()
        
        # 设置试运行模式
        generator.set_option('dry_run', True)
        
        # 执行代码生成
        result = generator.generate()
        
        if result.success:
            print(f"✅ 试运行成功!")
            print(f"   在试运行模式下，不会实际生成文件")
            print(f"   模拟耗时: {result.generation_time:.2f} 秒")
            
            if result.warnings:
                print(f"\n⚠️  试运行警告 ({len(result.warnings)} 个):")
                for warning in result.warnings:
                    print(f"   - {warning}")
                    
        else:
            print(f"❌ 试运行失败!")
            for error in result.errors:
                print(f"   - {error}")
            return False
            
    except Exception as e:
        print(f"❌ 试运行过程中发生异常: {e}")
        return False
    
    return True


def config_analysis_example():
    """配置文件分析示例"""
    print("\n=== 配置文件分析示例 ===")
    
    try:
        # 直接使用配置解析器
        config_parser = ConfigParser()
        config_data = config_parser.load_config()
        
        print(f"✅ 配置文件加载成功!")
        print(f"   文件路径: {config_parser.config_path}")
        
        # 显示基本信息
        print(f"\n基本信息:")
        print(f"   产品名称: {config_parser.get_product_name()}")
        print(f"   SoC型号: {config_parser.get_soc()}")
        print(f"   SoC厂商: {config_parser.get_soc_company()}")
        print(f"   设备厂商: {config_parser.get_device_company()}")
        print(f"   目标架构: {config_parser.get_target_cpu()}")
        print(f"   系统版本: {config_parser.get_ohos_version()}")
        
        # 显示模块状态
        enabled_modules = config_parser.get_enabled_modules()
        print(f"\n模块状态:")
        for module, enabled in enabled_modules.items():
            status = "✅ 启用" if enabled else "❌ 禁用"
            print(f"   {module}: {status}")
        
        # 显示音频相关配置
        if config_parser.is_module_enabled('audio_drivers'):
            print(f"\n音频驱动配置:")
            print(f"   编解码芯片: {config_parser.get_codec_chip()}")
            print(f"   兼容驱动: {config_parser.get_compatible_driver()}")
            print(f"   总线类型: {config_parser.get_bus_type()}")
            print(f"   ADM驱动: {'启用' if config_parser.is_sub_module_enabled('audio_drivers', 'adm_drivers') else '禁用'}")
            print(f"   ALSA驱动: {'启用' if config_parser.is_sub_module_enabled('audio_drivers', 'alsa_drivers') else '禁用'}")
        
        # 显示摄像头相关配置
        if config_parser.is_module_enabled('camera_drivers'):
            print(f"\n摄像头驱动配置:")
            print(f"   摄像头芯片: {config_parser.get_camera_chip()}")
        
    except Exception as e:
        print(f"❌ 配置分析过程中发生异常: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("OpenHarmony设备代码生成器 - 使用示例")
    print("=" * 50)
    
    # 检查项目结构
    project_root = Path(__file__).parent.parent
    config_file = project_root / "config" / "orangepi_5b.yaml"
    template_dir = project_root / "template"
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        print("请确保在正确的项目目录中运行此示例")
        return 1
    
    if not template_dir.exists():
        print(f"❌ 模板目录不存在: {template_dir}")
        print("请确保在正确的项目目录中运行此示例")
        return 1
    
    # 设置工作目录
    os.chdir(project_root)
    
    try:
        # 运行各种示例
        success_count = 0
        total_examples = 4
        
        # 1. 配置文件分析
        if config_analysis_example():
            success_count += 1
        
        # 2. 预览生成内容
        if preview_generation_example():
            success_count += 1
        
        # 3. 试运行
        if dry_run_example():
            success_count += 1
        
        # 4. 实际生成
        if basic_generation_example():
            success_count += 1
        
        # 总结
        print(f"\n{'='*50}")
        print(f"示例执行完成: {success_count}/{total_examples} 个成功")
        
        if success_count == total_examples:
            print("✅ 所有示例都执行成功!")
            print(f"\n生成的文件保存在: {project_root}/output/")
            return 0
        else:
            print("⚠️  部分示例执行失败，请检查错误信息")
            return 1
            
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 示例执行过程中发生未知错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())