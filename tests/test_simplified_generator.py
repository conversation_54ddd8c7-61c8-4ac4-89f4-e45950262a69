#!/usr/bin/env python3
"""
简化代码生成器测试脚本

用于验证简化版本的功能是否正常
"""

import sys
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from oh_codegen.simplified_generator import SimpleCodeGenerator, generate_device_code


def test_basic_functionality():
    """测试基础功能"""
    print("=== 测试基础功能 ===")
    
    try:
        # 测试便捷函数
        result = generate_device_code("orangepi_5b.yaml", clean_output=True)
        
        print(f"生成状态: {'成功' if result['success'] else '失败'}")
        print(f"生成文件数: {len(result['generated_files'])}")
        print(f"执行脚本数: {len(result['executed_scripts'])}")
        print(f"错误数: {len(result['errors'])}")
        
        if result['errors']:
            print("错误详情:")
            for error in result['errors']:
                print(f"  - {error}")
        
        return result['success']
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_generator_class():
    """测试生成器类"""
    print("\n=== 测试生成器类 ===")
    
    try:
        generator = SimpleCodeGenerator()
        
        # 测试配置加载
        config_data = generator.load_config("orangepi_5b.yaml")
        print(f"产品名称: {config_data.get('product_name')}")
        print(f"SoC: {config_data.get('soc')}")
        
        # 测试启用模块获取
        enabled_modules = generator.get_enabled_modules()
        print(f"启用的模块: {enabled_modules}")
        
        # 测试模板变量
        print(f"模板变量数量: {len(generator.template_variables)}")
        
        # 检查关键变量
        key_vars = ['product_name', 'soc', 'codec_chip']
        for var in key_vars:
            value = generator.template_variables.get(var, 'N/A')
            print(f"  {var}: {value}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_preview_mode():
    """测试预览模式"""
    print("\n=== 测试预览模式 ===")
    
    try:
        generator = SimpleCodeGenerator()
        generator.load_config("orangepi_5b.yaml")
        
        print("预览信息:")
        print(f"  产品名称: {generator.config_data.get('product_name')}")
        print(f"  启用模块: {generator.get_enabled_modules()}")

        # 构建正确的输出目录路径
        product_name = generator.config_data.get('product_name', 'unknown')
        ohos_version = generator.config_data.get('ohos_version', '5.0.0')
        output_path = generator.output_dir / ohos_version / product_name / "device"
        print(f"  输出目录: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")

    try:
        # 测试不存在的配置文件
        result = generate_device_code("nonexistent.yaml")

        if not result['success'] and len(result['errors']) > 0:
            print("✓ 正确处理了不存在的配置文件")
            print(f"  错误信息: {result['errors'][0]}")
            return True
        else:
            print("错误：应该返回失败结果但没有")
            return False

    except Exception as e:
        print(f"测试失败: {e}")
        return False


def verify_generated_files():
    """验证生成的文件"""
    print("\n=== 验证生成的文件 ===")

    # 新的目录结构：output/5.0.0/orangepi_5b/device/
    output_dir = Path("output/5.0.0/orangepi_5b/device")

    if not output_dir.exists():
        print("错误：输出目录不存在")
        return False

    # 检查关键文件
    expected_files = [
        "Makefile",
        "codec/include/rk809_codec.h",
        "codec/src/rk809_codec_impl.c",
        "dai/include/rk3588s_dai_ops.h",
        "include/rk3588s_audio_common.h"
    ]

    missing_files = []
    for file_path in expected_files:
        full_path = output_dir / file_path
        if not full_path.exists():
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")

    if missing_files:
        print("缺少的文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False

    print("所有关键文件都已生成")
    print(f"✓ 新的目录结构正确: {output_dir}")
    return True


def main():
    """主测试函数"""
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    print("开始测试简化代码生成器...")
    
    tests = [
        ("基础功能", test_basic_functionality),
        ("生成器类", test_generator_class),
        ("预览模式", test_preview_mode),
        ("错误处理", test_error_handling),
        ("文件验证", verify_generated_files),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！简化代码生成器工作正常。")
        return 0
    else:
        print("❌ 部分测试失败，请检查问题。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
