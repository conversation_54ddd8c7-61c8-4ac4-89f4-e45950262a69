#!/usr/bin/env python3
"""
模块化处理器测试脚本

测试新的模块化处理器架构是否正常工作
"""

import sys
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from oh_codegen.simplified_generator import SimpleCodeGenerator, generate_device_code
from oh_codegen.processors import list_supported_devices, get_processor


def test_processor_registration():
    """测试处理器注册"""
    print("=== 测试处理器注册 ===")
    
    supported_devices = list_supported_devices()
    print(f"支持的设备类型: {supported_devices}")
    
    expected_devices = ['audio_drivers', 'camera_drivers', 'kernel_drivers']
    for device in expected_devices:
        if device in supported_devices:
            print(f"✓ {device} 处理器已注册")
        else:
            print(f"✗ {device} 处理器未注册")
            return False
    
    return True


def test_processor_creation():
    """测试处理器创建"""
    print("\n=== 测试处理器创建 ===")
    
    try:
        # 测试音频处理器
        audio_processor_class = get_processor('audio_drivers')
        print(f"✓ 音频处理器类: {audio_processor_class.__name__}")
        
        # 测试摄像头处理器
        camera_processor_class = get_processor('camera_drivers')
        print(f"✓ 摄像头处理器类: {camera_processor_class.__name__}")
        
        # 测试内核处理器
        kernel_processor_class = get_processor('kernel_drivers')
        print(f"✓ 内核处理器类: {kernel_processor_class.__name__}")
        
        # 测试不存在的处理器
        try:
            get_processor('nonexistent_device')
            print("✗ 应该抛出异常但没有")
            return False
        except ValueError:
            print("✓ 正确处理了不存在的设备类型")
        
        return True
        
    except Exception as e:
        print(f"✗ 处理器创建失败: {e}")
        return False


def test_adm_framework():
    """测试ADM框架"""
    print("\n=== 测试ADM框架 ===")

    try:
        result = generate_device_code("orangepi_5b.yaml", clean_output=True)

        if result['success']:
            print("✓ ADM框架生成成功")

            # 检查是否生成到audio_drivers目录
            output_dir = Path("output/5.0.0/rk3568/device")  # 注意：配置文件中product_name是rk3568
            audio_dir = output_dir / "audio_drivers"

            if audio_dir.exists():
                print("✓ 生成到audio_drivers目录")

                # 检查codec芯片特定目录
                codec_dir = audio_dir / "codec" / "rk809_codec"
                if codec_dir.exists():
                    print("✓ 生成到codec/rk809_codec目录")

                    # 检查compatible_driver变量是否正确替换
                    impl_file = codec_dir / "src" / "rk809_codec_impl.c"
                    if impl_file.exists():
                        content = impl_file.read_text()
                        if 'rk817_codec.h' in content:
                            print("✓ compatible_driver变量正确替换为rk817")
                            return True
                        else:
                            print("✗ compatible_driver变量未正确替换")
                            return False
                    else:
                        print("✗ 未找到codec实现文件")
                        return False
                else:
                    print("✗ 未找到codec/rk809_codec目录")
                    return False
            else:
                print("✗ 未找到audio_drivers目录")
                return False
        else:
            print(f"✗ ADM框架生成失败: {result['errors']}")
            return False

    except Exception as e:
        print(f"✗ ADM框架测试失败: {e}")
        return False


def test_alsa_framework():
    """测试ALSA框架"""
    print("\n=== 测试ALSA框架 ===")
    
    try:
        result = generate_device_code("test_alsa.yaml", clean_output=True)
        
        if result['success']:
            print("✓ ALSA框架生成成功")
            
            # 检查是否生成到audio_alsa目录
            output_dir = Path("output/5.0.0/test_alsa_device/device")
            audio_dir = output_dir / "audio_alsa"
            
            if audio_dir.exists():
                print("✓ 生成到audio_alsa目录")
                
                # 检查codec_chip是否正确
                codec_files = list(audio_dir.glob("**/es8323*"))
                if codec_files:
                    print("✓ 使用了正确的codec_chip (es8323)")
                    return True
                else:
                    print("✗ 未找到es8323相关文件")
                    return False
            else:
                print("✗ 未找到audio_alsa目录")
                return False
        else:
            print(f"✗ ALSA框架生成失败: {result['errors']}")
            return False
            
    except Exception as e:
        print(f"✗ ALSA框架测试失败: {e}")
        return False


def test_camera_processor():
    """测试摄像头处理器"""
    print("\n=== 测试摄像头处理器 ===")

    try:
        result = generate_device_code("orangepi_5b.yaml", clean_output=False)

        if result['success']:
            print("✓ 摄像头处理器工作正常")

            # 检查camera模板目录是否存在
            camera_template_dir = Path("template/device/board/camera")
            if not camera_template_dir.exists() or not any(camera_template_dir.iterdir()):
                print("✓ camera模板目录为空，这是正常的")
                return True

            # 如果模板目录不为空，检查是否生成到camera目录
            output_dir = Path("output/5.0.0/orangepi_5b/device")
            camera_dir = output_dir / "camera"

            if camera_dir.exists():
                print("✓ 生成到camera目录")
                return True
            else:
                print("✗ 未找到camera目录")
                return False
        else:
            print(f"✗ 摄像头处理器失败: {result['errors']}")
            return False

    except Exception as e:
        print(f"✗ 摄像头处理器测试失败: {e}")
        return False


def test_processor_fallback():
    """测试处理器回退机制"""
    print("\n=== 测试处理器回退机制 ===")
    
    try:
        # 创建一个包含不支持设备类型的配置
        generator = SimpleCodeGenerator()
        
        # 模拟一个不存在处理器的模块
        fake_config = {
            'board_code': [
                {'unsupported_device': {'enabled': True}}
            ]
        }
        
        generator.config_data = fake_config
        generator.template_variables = generator._build_template_variables()
        
        enabled_modules = generator.get_enabled_modules()
        if 'unsupported_device' in enabled_modules:
            print("✓ 检测到不支持的设备类型")
            
            # 测试回退到通用处理方法
            output_dir = Path("output/test")
            result = generator._process_module_with_processor('unsupported_device', output_dir)
            
            # 应该回退到通用方法，不会出错
            print("✓ 成功回退到通用处理方法")
            return True
        else:
            print("✗ 未检测到不支持的设备类型")
            return False
            
    except Exception as e:
        print(f"✗ 处理器回退测试失败: {e}")
        return False


def test_processor_configuration():
    """测试处理器配置解析"""
    print("\n=== 测试处理器配置解析 ===")
    
    try:
        generator = SimpleCodeGenerator()
        generator.load_config("orangepi_5b.yaml")
        
        # 测试获取模块配置
        audio_config = generator._get_module_config('audio_drivers')
        if audio_config:
            print("✓ 成功获取音频模块配置")
            print(f"  ADM启用: {audio_config.get('adm_drivers', {}).get('enable', False)}")
            print(f"  ALSA启用: {audio_config.get('alsa_drivers', {}).get('enable', False)}")
        else:
            print("✗ 未找到音频模块配置")
            return False
        
        camera_config = generator._get_module_config('camera_drivers')
        if camera_config:
            print("✓ 成功获取摄像头模块配置")
            print(f"  摄像头芯片: {camera_config.get('camera_chip', 'N/A')}")
        else:
            print("✗ 未找到摄像头模块配置")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 处理器配置解析测试失败: {e}")
        return False


def main():
    """主测试函数"""
    # 设置日志
    logging.basicConfig(level=logging.WARNING, format='%(levelname)s: %(message)s')
    
    print("开始测试模块化处理器架构...")
    
    tests = [
        ("处理器注册", test_processor_registration),
        ("处理器创建", test_processor_creation),
        ("ADM框架", test_adm_framework),
        ("ALSA框架", test_alsa_framework),
        ("摄像头处理器", test_camera_processor),
        ("处理器回退机制", test_processor_fallback),
        ("处理器配置解析", test_processor_configuration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"运行测试: {test_name}")
        print('='*60)
        
        try:
            if test_func():
                print(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*60)
    
    if passed == total:
        print("🎉 所有测试通过！模块化处理器架构工作正常。")
        return 0
    else:
        print("❌ 部分测试失败，请检查问题。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
