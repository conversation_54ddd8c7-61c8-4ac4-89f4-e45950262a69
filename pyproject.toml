[build-system]
requires = [
    "setuptools>=42",
    "wheel"
]
build-backend = "setuptools.build_meta"

[project]
name = "oh-codegen"
version = "1.0.0"
description = "OpenHarmony设备代码生成器"
readme = "README.md"
license = {file = "LICENSE.txt"}
authors = [
    {name = "OpenHarmony Device Code Generator Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Code Generators",
    "Topic :: System :: Hardware :: Hardware Drivers",
]
requires-python = ">=3.8"
dependencies = [
    "PyYAML>=6.0",
    "Jinja2>=3.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
]

[project.urls]
Homepage = "https://gitee.com/openharmony"
Repository = "https://gitee.com/openharmony/device_code_generator"
Documentation = "https://gitee.com/openharmony/device_code_generator/blob/master/README.md"
Issues = "https://gitee.com/openharmony/device_code_generator/issues"

[project.scripts]
oh-codegen = "oh_codegen.cli:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--strict-markers",
    "--cov=oh_codegen",
    "--cov-report=term-missing",
    "--cov-report=html",
]

[tool.black]
line-length = 100
target-version = ["py38"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | output
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = ["yaml.*"]
ignore_missing_imports = true
