# 自定义代码修改功能使用指南

## 概述

自定义代码修改功能允许开发者在代码生成后自动应用自定义的函数替换和添加，确保修改在下次代码生成时不会丢失。

## 功能特性

### ✅ 支持的操作
- **函数替换**: 替换生成代码中的现有函数实现
- **函数添加**: 在指定位置添加新的函数实现
- **头文件修改**: 在头文件中添加声明和成员变量
- **包含文件管理**: 自动添加所需的头文件包含
- **多文件支持**: 可以同时修改多个文件
- **持久化保存**: 修改在下次代码生成时自动重新应用

### 🎯 设计目标
- 避免每次重新生成代码时丢失自定义修改
- 不需要修改代码模板就能持久化保存自定义实现
- 支持复杂的代码修改场景

## 配置文件格式

### 基本结构

```yaml
# 自定义代码配置文件
version: "1.0"
product_name: "your_product_name"  # 可选，留空表示适用于所有产品

code_modifications:
  # 函数替换配置
  function_replacements:
    - file_path: "output/5.0.0/product/board/module/file.cpp"
      function_signature: "RetCode ClassName::FunctionName(const int32_t param)"
      description: "替换描述"
      replacement_code: |
        RetCode ClassName::FunctionName(const int32_t param)
        {
            // 自定义实现
            return RC_OK;
        }
      includes:
        - "#include \"custom_header.h\""

  # 函数添加配置
  function_additions:
    - file_path: "output/5.0.0/product/board/module/file.cpp"
      position: "end_of_file"  # 插入位置
      description: "添加描述"
      code: |
        RetCode ClassName::NewFunction(const int32_t param)
        {
            // 新增函数实现
            return RC_OK;
        }
      includes:
        - "#include <new_header.h>"

  # 头文件修改配置
  header_modifications:
    - file_path: "output/5.0.0/product/board/module/file.h"
      description: "头文件修改描述"
      additions:
        - position: "private_members"
          code: "int customVariable_;"
        - position: "public_methods"
          code: "RetCode NewFunction(const int32_t param);"
```

### 插入位置选项

#### 对于函数添加 (`position`)
- `"end_of_file"`: 文件末尾
- `"end_of_class"`: 类定义末尾
- `"after_function:function_name"`: 指定函数之后
- `"before_function:function_name"`: 指定函数之前

#### 对于头文件修改 (`position`)
- `"public_methods"`: public部分的方法区域
- `"private_members"`: private部分的成员变量区域
- `"protected_methods"`: protected部分的方法区域

## 文件组织结构

### 📁 配置文件命名规范

```
config/
├── product_name.yaml                    # 主配置文件
├── product_name.custom.yaml             # 对应的自定义代码配置
└── product_name.custom.yaml.example     # 示例配置文件
```

### 🔗 自动关联机制

系统会根据主配置文件自动查找对应的自定义配置：

1. **运行**: `python -m src.oh_codegen rk3568.yaml`
2. **自动查找**: `config/rk3568.custom.yaml`
3. **备选方案**: 根目录的 `custom_code_config.yaml` (向后兼容)

## 使用步骤

### 1. 创建配置文件

根据你的产品名称创建自定义配置文件：

```bash
# 方法1: 基于配置文件名
cp config/product_name.custom.yaml.example config/your_product.custom.yaml

# 方法2: 基于产品名称 (推荐)
cp config/product_name.custom.yaml.example config/rk3568.custom.yaml
```

### 2. 配置修改内容

编辑你的自定义配置文件，添加需要的修改：

```yaml
# config/rk3568.custom.yaml
version: "1.0"
product_name: "rk3568"  # 与主配置文件中的product_name一致

code_modifications:
  function_replacements:
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_preview_node.cpp"
      function_signature: "RetCode MyPreviewNode::Stop(const int32_t streamId)"
      replacement_code: |
        RetCode MyPreviewNode::Stop(const int32_t streamId)
        {
            CAMERA_LOGI("Custom stop implementation");
            // 你的自定义逻辑
            return RC_OK;
        }
```

### 3. 运行代码生成

正常运行代码生成命令，系统会自动查找并应用对应的自定义配置：

```bash
# 运行主配置，自动应用 config/rk3568.custom.yaml
python -m src.oh_codegen rk3568.yaml --clean

# 运行测试配置，自动应用 config/test_board_structure.custom.yaml
python -m src.oh_codegen test_kernel.yaml --clean
```

自定义修改会在代码生成完成后自动应用。

### 4. 查看应用结果

系统会输出详细的日志信息：

```
INFO - 找到产品名称匹配的自定义配置: config/rk3568.custom.yaml
INFO - 成功加载自定义代码配置: config/rk3568.custom.yaml
INFO - 配置包含: 1 个函数替换, 1 个函数添加, 0 个头文件修改
INFO - 成功替换函数: RetCode MyPreviewNode::Stop(const int32_t streamId)
INFO - 所有自定义代码修改应用成功
```

## 实际示例

### 示例1: 函数替换

替换摄像头预览节点的停止函数：

```yaml
function_replacements:
  - file_path: "output/5.0.0/test_board_structure/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_preview_node.cpp"
    function_signature: "RetCode MyPreviewNode::Stop(const int32_t streamId)"
    description: "自定义预览节点停止逻辑"
    replacement_code: |
      RetCode MyPreviewNode::Stop(const int32_t streamId)
      {
          CAMERA_LOGI("Custom Stop implementation for preview stream %d", streamId);
          
          // 自定义停止逻辑
          if (streamId < 0) {
              CAMERA_LOGE("Invalid stream ID: %d", streamId);
              return RC_ERROR;
          }
          
          // 执行清理工作
          CAMERA_LOGI("Cleaning up preview resources...");
          
          CAMERA_LOGI("Preview node stopped successfully");
          return RC_OK;
      }
```

### 示例2: 函数添加

在捕获节点中添加自定义处理函数：

```yaml
function_additions:
  - file_path: "output/5.0.0/test_board_structure/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.cpp"
    position: "end_of_file"
    description: "添加自定义捕获处理函数"
    code: |
      // 自定义捕获处理函数
      RetCode MyCaptureNode::CustomCaptureProcess(std::shared_ptr<IBuffer>& buffer)
      {
          CAMERA_LOGI("Custom capture processing started");
          
          if (buffer == nullptr) {
              CAMERA_LOGE("Buffer is null");
              return RC_ERROR;
          }
          
          // 自定义处理逻辑
          CAMERA_LOGI("Processing buffer with custom logic");
          
          return RC_OK;
      }
    includes:
      - "#include <memory>"
```

### 示例3: 头文件修改

在头文件中添加函数声明：

```yaml
header_modifications:
  - file_path: "output/5.0.0/test_board_structure/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.h"
    description: "添加自定义函数声明"
    additions:
      - position: "private_members"
        code: |
          // 自定义成员变量
          int customParam_;
          
          // 自定义方法声明
          RetCode CustomCaptureProcess(std::shared_ptr<IBuffer>& buffer);
```

## 注意事项

### ⚠️ 重要提醒

1. **函数签名匹配**: 函数替换时，`function_signature` 必须与源代码中的函数签名完全匹配
2. **文件路径**: 确保 `file_path` 指向正确的生成文件路径
3. **缩进格式**: 代码会自动调整缩进，但建议保持一致的代码风格
4. **产品名称**: 如果设置了 `product_name`，只有匹配的产品才会应用修改

### 🔧 故障排除

1. **函数替换失败**: 检查函数签名是否完全匹配，包括参数类型和修饰符
2. **文件不存在**: 确认文件路径正确，文件已被生成
3. **插入位置错误**: 检查 `position` 参数是否正确，目标位置是否存在

## 日志信息

运行时会输出详细的日志信息：

```
INFO - 成功加载自定义代码配置: custom_code_config.yaml
INFO - 配置包含: 1 个函数替换, 1 个函数添加, 0 个头文件修改
INFO - 成功替换函数: RetCode MyPreviewNode::Stop(const int32_t streamId)
INFO - 成功添加函数到: my_capture_node.cpp at position end_of_file
INFO - 所有自定义代码修改应用成功
```

## 高级用法

### 条件应用

通过设置 `product_name` 可以让配置只对特定产品生效：

```yaml
product_name: "rk3568"  # 只对rk3568产品应用修改
```

### 多文件修改

可以在一个配置文件中修改多个文件：

```yaml
code_modifications:
  function_replacements:
    - file_path: "output/5.0.0/product/board/camera/file1.cpp"
      # ... 配置1
    - file_path: "output/5.0.0/product/board/audio/file2.cpp"
      # ... 配置2
```

## 📁 文件结构总览

```
device_code_generator/
├── config/
│   ├── test_kernel.yaml                          # 主配置文件
│   ├── test_board_structure.custom.yaml          # 对应的自定义配置
│   ├── orangepi_5b.yaml                         # 另一个主配置
│   ├── orangepi_5b.custom.yaml.example          # Orange Pi 5B示例
│   └── product_name.custom.yaml.example         # 通用示例模板
├── src/oh_codegen/custom_code/                  # 自定义代码模块
│   ├── __init__.py
│   ├── config.py                                # 配置数据结构
│   ├── cpp_parser.py                            # C++代码解析器
│   └── code_modifier.py                         # 代码修改器
├── custom_code_config.yaml.example              # 根目录通用示例
└── CUSTOM_CODE_GUIDE.md                         # 本使用指南
```

## 🎯 总结

这个功能大大提升了开发效率，让你的自定义修改能够持久化保存！

### ✨ 核心优势：
- **🔄 持久化**: 修改在重新生成时自动重新应用
- **📁 组织化**: 配置文件与主配置文件对应，便于管理
- **🎯 精确性**: 基于函数签名的精确匹配和替换
- **🔧 灵活性**: 支持函数替换、添加和头文件修改
- **🚀 自动化**: 无需手动干预，代码生成后自动应用

### 🌟 适用场景：
- 设备特定的硬件适配代码
- 性能优化的自定义实现
- 调试和日志增强
- 错误处理改进
- 业务逻辑定制

让你的OpenHarmony设备适配开发更加高效和可维护！
