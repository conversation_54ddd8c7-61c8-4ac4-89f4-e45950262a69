# 实际支持的所有配置用法

## 1. 完整的配置文件结构

```
# config/rk3568.custom.yaml
version: "1.0"                    # 版本号（必需）
product_name: "rk3568"           # 产品名称（必需，与主配置一致）

code_modifications:              # 代码修改配置（必需）
  
  # 函数替换配置
  function_replacements:
    - file_path: "相对路径"       # 目标文件路径（必需）
      function_signature: "函数签名"  # 完整函数签名（必需）
      description: "描述"         # 替换说明（可选）
      replacement_code: |         # 替换代码（必需）
        // 新的函数实现
      includes:                   # 头文件包含（可选）
        - "#include \"header.h\""
  
  # 函数添加配置
  function_additions:
    - file_path: "相对路径"       # 目标文件路径（必需）
      position: "插入位置"        # 插入位置（必需）
      description: "描述"         # 添加说明（可选）
      code: |                     # 添加代码（必需）
        // 新增的函数
      includes:                   # 头文件包含（可选）
        - "#include \"header.h\""
  
  # 头文件修改配置
  header_modifications:
    - file_path: "头文件路径"     # 目标头文件路径（必需）
      description: "描述"         # 修改说明（可选）
      additions:                  # 添加内容列表（必需）
        - position: "插入位置"    # 插入位置（必需）
          code: |                 # 添加代码（必需）
            // 新增的声明
```
## 2. function_replacements 详细用法

```
function_replacements:
  - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_exif_node.cpp"
    function_signature: "void MyExifNode::DeliverBuffer(std::shared_ptr<IBuffer>& buffer)"
    description: "自定义 EXIF 节点缓冲区处理逻辑"  # 可选
    replacement_code: |
      void MyExifNode::DeliverBuffer(std::shared_ptr<IBuffer>& buffer)
      {
          if (buffer == nullptr) {
              CAMERA_LOGE("Custom EXIF buffer is null");
              return;
          }
          
          // 自定义处理逻辑
          ProcessCustomExifData(buffer);
          return NodeBase::DeliverBuffer(buffer);
      }
    includes:  # 可选，自动添加到文件顶部
      - "#include \"custom_exif_processor.h\""
      - "#include <memory>"
      - "#include <vector>"
```

## 3. function_additions 详细用法
```
function_additions:
  - file_path: "output/5.0.0/rk3568/board/camera/.../my_exif_node.cpp"
    position: "end_of_file"  # 支持的位置类型
    description: "添加自定义处理函数"  # 可选
    code: |
      // 自定义函数实现
      bool MyExifNode::ProcessCustomExifData(std::shared_ptr<IBuffer>& buffer)
      {
          CAMERA_LOGI("Processing custom EXIF data");
          // 处理逻辑
          return true;
      }
      
      void MyExifNode::InitializeCustomProcessor()
      {
          CAMERA_LOGI("Initializing custom processor");
          // 初始化逻辑
      }
    includes:  # 可选
      - "#include \"custom_processor.h\""
```
## 4. header_modifications 详细用法

```
header_modifications:
  - file_path: "output/5.0.0/rk3568/board/camera/.../my_exif_node.h"
    description: "添加自定义成员和方法声明"  # 可选
    additions:
      - position: "private_members"  # 插入到私有成员区域
        code: |
          // 自定义成员变量
          std::unique_ptr<CustomExifProcessor> customProcessor_;
          std::mutex processingMutex_;
          
          // 自定义方法声明
          bool ProcessCustomExifData(std::shared_ptr<IBuffer>& buffer);
          void InitializeCustomProcessor();
          void CleanupCustomProcessor();
```
✅ 支持的插入位置 (position)

对于 function_additions：


`"end_of_file"` - 文件末尾

`"after_includes"` - 包含文件之后

`"before_class"` - 类定义之前

`"after_class"` - 类定义之后


对于 header_modifications：


`"private_members"` - 私有成员区域

`"public_members"` - 公有成员区域

`"protected_members"` - 保护成员区域

`"end_of_class"` - 类定义末尾

`"end_of_file"` - 文件末尾


## includes 的处理机制

自动去重
```
# 即使多个替换指定相同的 include，也只会添加一次
function_replacements:
  - function_signature: "void MyExifNode::DeliverBuffer(...)"
    includes:
      - "#include \"common.h\""
  - function_signature: "void MyExifNode::Config(...)"
    includes:
      - "#include \"common.h\""  # 不会重复添加
```
智能插入位置
```
// 原文件
#include "my_exif_node.h"
#include "camera_log.h"

// 自动插入新的 includes（在现有 includes 之后）
#include "my_exif_node.h"
#include "camera_log.h"
#include "custom_processor.h"  // ← 新添加
#include <memory>              // ← 新添加

// 类定义...
```
✅ 实际使用示例

完整的替换示例
```
version: "1.0"
product_name: "rk3568"

code_modifications:
  function_replacements:
    # 替换 EXIF 节点的 DeliverBuffer 函数
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_exif_node.cpp"
      function_signature: "void MyExifNode::DeliverBuffer(std::shared_ptr<IBuffer>& buffer)"
      replacement_code: |
        void MyExifNode::DeliverBuffer(std::shared_ptr<IBuffer>& buffer)
        {
            ProcessCustomExifData(buffer);
            return NodeBase::DeliverBuffer(buffer);
        }
      includes:
        - "#include \"custom_exif_processor.h\""
    
    # 替换 Config 函数
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_exif_node.cpp"
      function_signature: "RetCode MyExifNode::Config(const int32_t streamId, const CaptureMeta& meta)"
      replacement_code: |
        RetCode MyExifNode::Config(const int32_t streamId, const CaptureMeta& meta)
        {
            InitializeCustomProcessor();
            return RC_OK;
        }

  function_additions:
    # 添加自定义函数
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_exif_node.cpp"
      position: "end_of_file"
      code: |
        bool MyExifNode::ProcessCustomExifData(std::shared_ptr<IBuffer>& buffer)
        {
            // 自定义处理逻辑
            return true;
        }

  header_modifications:
    # 修改头文件
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_exif_node.h"
      additions:
        - position: "private_members"
          code: |
            bool ProcessCustomExifData(std::shared_ptr<IBuffer>& buffer);
```