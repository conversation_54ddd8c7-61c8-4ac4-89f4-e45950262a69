# 摄像头Node模板生成总结

## 任务完成概述

成功分析了OpenHarmony源码中的摄像头node文件规律，创建了Jinja2模板，并修改了camera_processor.py以支持根据YAML配置动态生成不同类型的node文件。

## 源码分析结果

### 分析的源码文件
- `rk_codec_node.h/cpp` - 编解码节点
- `rk_face_node.h/cpp` - 人脸检测节点  
- `rk_exif_node.h/cpp` - EXIF信息节点
- `rk_scale_node.h/cpp` - 缩放节点

### 发现的规律
1. **类名规律**: `RK{NodeName}Node` → 模板化为 `My{NodeName}Node`
2. **文件名规律**: `rk_{node_name}_node.h/cpp` → `my_{node_name}_node.h/cpp`
3. **头文件保护宏**: `HOS_CAMERA_RK{NODENAME}_NODE_H` → `HOS_CAMERA_MY{NODENAME}_NODE_H`
4. **基础结构**: 都继承自 `NodeBase`，有相同的基础方法
5. **注册宏**: `REGISTERNODE(RK{NodeName}Node, {"RK{NodeName}"})` → `REGISTERNODE(My{NodeName}Node, {"My{NodeName}"})`

## 创建的模板

### 1. 头文件模板 (`my_{{node_name}}_node.h.j2`)

**特点**:
- 动态头文件保护宏: `#ifndef HOS_CAMERA_MY{{ node_name|upper }}_NODE_H`
- 条件包含: 根据node_name包含不同的头文件和定义
- 类名模板化: `class My{{ node_name|title }}Node : public NodeBase`
- 特定功能支持:
  - `codec`: 包含RGA和MPP相关头文件
  - `face`: 包含人脸检测相关枚举和元数据标签
  - `exif`: 包含GPS相关枚举

### 2. 源文件模板 (`my_{{node_name}}_node.cpp.j2`)

**特点**:
- 版权信息: 动态年份 `Copyright (c) {{ year }}`
- 构造函数模板化: `My{{ node_name|title }}Node::My{{ node_name|title }}Node(...)`
- 条件方法实现:
  - `codec`: JPEG/H264编码相关方法
  - `face`: 人脸检测元数据处理方法
  - `exif`: GPS信息处理方法
- 注册宏: `REGISTERNODE(My{{ node_name|title }}Node, {"My{{ node_name|title }}"})`

## 代码修改

### 1. 简化变量生成器增强

**文件**: `src/oh_codegen/simplified_generator.py`

```python
# 摄像头相关的简化变量
if 'camera_drivers_node' in variables:
    variables['nodes'] = variables['camera_drivers_node']

# 添加年份变量
from datetime import datetime
variables['year'] = datetime.now().year
```

### 2. 摄像头处理器增强

**文件**: `src/oh_codegen/processors/camera_processor.py`

**新增方法**:
- `_process_camera_nodes()`: 处理摄像头节点列表
- `_process_single_node()`: 处理单个摄像头节点

**核心逻辑**:
```python
def _process_camera_nodes(self, nodes: list, output_dir: Path, result: ProcessResult):
    # 为每个节点生成文件
    for node_name in nodes:
        self._process_single_node(node_name, node_template_dir, node_output_dir, result)

def _process_single_node(self, node_name: str, template_dir: Path, output_dir: Path, result: ProcessResult):
    # 设置节点特定的模板变量
    self.template_variables['node_name'] = node_name
    # 处理节点模板文件
    for template_file in template_dir.glob("my_{{node_name}}_node.*"):
        if template_file.suffix == '.j2':
            self._process_template_file(template_file, output_dir, result)
```

## 测试验证

### 1. YAML配置
```yaml
camera_drivers:
  enabled: true
  sensor: rkispv6
  node:
    - face
    - exif
    - codec
```

### 2. 生成结果
**成功生成14个文件**:
- 基础摄像头文件: 8个
- Node文件: 6个 (face, exif, codec 各2个文件)

### 3. 生成的Node文件
```
output/5.0.0/test_sensor_device/device/camera/pipeline_core/src/node/
├── my_codec_node.cpp
├── my_codec_node.h
├── my_exif_node.cpp
├── my_exif_node.h
├── my_face_node.cpp
└── my_face_node.h
```

## 模板特性

### 1. 条件生成
根据`node_name`的值生成不同的代码:

**Codec节点**:
```cpp
#include "RockchipRga.h"
#include "rk_mpi.h"
// ... codec特定的方法
void MyCodecNode::Yuv420ToJpeg(std::shared_ptr<IBuffer>& buffer);
```

**Face节点**:
```cpp
enum FaceRectanglesIndex : int32_t { INDEX_0 = 0, ... };
std::vector<uint32_t> FaceDetectMetadataTag = { ... };
// ... face特定的方法
RetCode MyFaceNode::GetFaceDetectMetaData(...);
```

**Exif节点**:
```cpp
enum GpsIndex : int32_t { LATITUDE_INDEX = 0, ... };
// ... exif特定的方法
RetCode MyExifNode::SetGpsInfoMetadata(...);
```

### 2. 变量替换效果

**模板变量**:
- `{{ node_name }}` → `codec`, `face`, `exif`
- `{{ node_name|title }}` → `Codec`, `Face`, `Exif`
- `{{ node_name|upper }}` → `CODEC`, `FACE`, `EXIF`
- `{{ year }}` → `2025`

**生成结果**:
- 类名: `MyCodecNode`, `MyFaceNode`, `MyExifNode`
- 文件名: `my_codec_node.h`, `my_face_node.cpp`
- 宏定义: `HOS_CAMERA_MYCODEC_NODE_H`
- 注册宏: `REGISTERNODE(MyCodecNode, {"MyCodec"})`

## 扩展性

### 1. 添加新Node类型
只需在YAML配置中添加新的node名称，模板会自动生成对应的基础结构。

### 2. 自定义Node实现
可以在模板的条件块中添加新的node类型特定代码:
```jinja2
{% elif node_name == 'your_new_node' %}
// 添加你的新节点特定代码
{% endif %}
```

### 3. 模板扩展
可以添加更多的模板文件，如BUILD.gn模板等。

## 使用方式

### 1. 配置YAML
```yaml
camera_drivers:
  enabled: true
  sensor: your_sensor_name
  node:
    - codec
    - face
    - exif
    - your_custom_node
```

### 2. 生成代码
```bash
python -m src.oh_codegen your_config.yaml --clean
```

### 3. 自定义实现
生成的代码包含了完整的框架结构和占位符实现，开发者可以在此基础上添加具体的业务逻辑。

## 总结

这次任务成功实现了：

1. ✅ **源码规律分析**: 深入分析了OpenHarmony中4种不同node的代码结构
2. ✅ **模板创建**: 创建了完整的Jinja2模板，支持条件生成
3. ✅ **处理器增强**: 修改了camera_processor.py以支持动态node生成
4. ✅ **变量系统**: 完善了变量传递和替换机制
5. ✅ **完整测试**: 验证了从配置到生成的完整流程

现在系统能够根据YAML配置中的node列表动态生成对应的摄像头节点代码，大大提高了开发效率和代码的一致性！
