# 摄像头选择性复制增强总结

## 改进概述

成功修改了摄像头的 `copy_file.sh` 脚本，实现了选择性复制指定子目录的功能，而不是复制整个 `v4l2` 目录。

## 需求分析

### 原始需求
- 复制 `$TEMPLATE_OHOS_PATH/device/board/hihope/rk3568/camera/vdi_impl/v4l2` 的指定子目录
- 指定目录以列表形式在脚本开头声明
- 只复制列表中指定的子目录及其内容

### 实际目录结构
通过检查OpenHarmony源码，发现 `v4l2` 目录下的实际子目录为：
- `demo` - 演示代码
- `device_manager` - 设备管理器
- `driver_adapter` - 驱动适配器
- `pipeline_core` - 管道核心

## 解决方案

### 1. 脚本头部声明子目录列表

```bash
# 指定要复制的子目录列表
# 只复制这些子目录及其内容，而不是复制整个v4l2目录
# 根据实际的OpenHarmony源码目录结构更新
COPY_SUBDIRS=(
    "demo"
    "device_manager"
    "driver_adapter"
    "pipeline_core"
)
```

**特点**：
- **列表形式声明**：在脚本开头以数组形式声明
- **注释说明**：清楚说明用途和来源
- **易于维护**：需要修改复制目录时只需更新这个列表

### 2. 选择性复制逻辑

```bash
# 复制指定的子目录
copied_count=0
for subdir in "${COPY_SUBDIRS[@]}"; do
    source_subdir="$SOURCE_BASE_DIR/$subdir"
    target_subdir="$TARGET_DIR/$subdir"
    
    if [ -d "$source_subdir" ]; then
        echo "复制子目录: $subdir"
        # 确保目标子目录存在
        mkdir -p "$target_subdir"
        # 复制子目录内容
        cp -r "$source_subdir"/* "$target_subdir/" 2>/dev/null || {
            echo "警告: 复制子目录 $subdir 时出现错误"
        }
        ((copied_count++))
    else
        echo "警告: 源子目录不存在: $source_subdir"
    fi
done
```

**特点**：
- **循环处理**：遍历声明的子目录列表
- **存在性检查**：只复制存在的子目录
- **错误处理**：对不存在或复制失败的情况给出警告
- **统计功能**：记录成功复制的子目录数量

### 3. 项目根目录参数支持

同时修复了基础处理器中的项目根目录参数传递问题：

```python
# 基础处理器修复
copy_script = template_dir / "copy_file.sh"
if copy_script.exists() and copy_script.stat().st_size > 0:
    # 传递项目根目录参数
    project_root = self.template_dir.parent  # 从template目录向上一级到项目根目录
    self._execute_script(copy_script, result, project_root)
```

## 实现效果

### 1. 脚本执行结果

**手动测试**：
```bash
cd template/device/board/camera
TEMPLATE_OHOS_VERSION=5.0.0 \
TEMPLATE_PRODUCT_NAME=rk3568 \
TEMPLATE_OHOS_PATH=/home/<USER>/ohos_5.0/openharmony-5.0.0 \
bash copy_file.sh /home/<USER>/ospp/device_code_generator
```

**输出结果**：
```
使用传入的项目根目录: /home/<USER>/ospp/device_code_generator
目标目录: /home/<USER>/ospp/device_code_generator/output/5.0.0/rk3568/device/camera
从 /home/<USER>/ohos_5.0/openharmony-5.0.0/device/board/hihope/rk3568/camera/vdi_impl/v4l2 复制指定的camera子目录到 /home/<USER>/ospp/device_code_generator/output/5.0.0/rk3568/device/camera
复制子目录: demo
复制子目录: device_manager
复制子目录: driver_adapter
复制子目录: pipeline_core
camera文件复制完成，成功复制 4 个子目录
```

### 2. 复制的目录结构

```
output/5.0.0/rk3568/device/camera/
├── demo/
│   ├── BUILD.gn
│   └── include/
├── device_manager/
│   ├── BUILD.gn
│   ├── include/
│   ├── src/
│   └── test/
├── driver_adapter/
│   └── test/
└── pipeline_core/
    ├── BUILD.gn
    ├── src/
    └── test/
```

### 3. 集成测试结果

**完整代码生成测试**：
```bash
python -m src.oh_codegen orangepi_5b.yaml --clean
```

**日志输出**：
```
16:13:51 - 执行脚本: /home/<USER>/ospp/device_code_generator/template/device/board/camera/copy_file.sh
16:13:51 - 传递项目根目录参数: /home/<USER>/ospp/device_code_generator
16:13:51 - 执行脚本: /home/<USER>/ospp/device_code_generator/template/device/board/camera/copy_file.sh
```

**结果**：✅ 成功执行2个脚本（ALSA + Camera）

## 技术特点

### 1. 灵活的配置方式
- **声明式配置**：在脚本头部声明要复制的目录
- **易于修改**：需要调整复制目录时只需修改数组
- **清晰可读**：代码意图明确，便于维护

### 2. 健壮的错误处理
- **存在性检查**：只处理存在的子目录
- **错误恢复**：单个子目录复制失败不影响其他子目录
- **详细日志**：提供清晰的执行状态反馈

### 3. 完整的参数支持
- **项目根目录参数**：支持Python传递的项目根目录参数
- **环境变量支持**：继承原有的环境变量机制
- **向后兼容**：保持对旧调用方式的支持

## 使用方式

### 1. 自动使用（推荐）
```python
from oh_codegen import generate_device_code
result = generate_device_code("orangepi_5b.yaml")
# 摄像头脚本会自动执行并复制指定的子目录
```

### 2. 手动测试
```bash
cd template/device/board/camera
TEMPLATE_OHOS_VERSION=5.0.0 \
TEMPLATE_PRODUCT_NAME=rk3568 \
TEMPLATE_OHOS_PATH=/path/to/ohos \
bash copy_file.sh /path/to/project/root
```

### 3. 自定义子目录列表
修改脚本头部的 `COPY_SUBDIRS` 数组：
```bash
COPY_SUBDIRS=(
    "demo"
    "device_manager"
    "driver_adapter"
    "pipeline_core"
    # 添加新的子目录
    "new_module"
)
```

## 扩展性

### 1. 添加新子目录
只需在 `COPY_SUBDIRS` 数组中添加新的目录名即可。

### 2. 条件复制
可以基于环境变量或配置参数动态调整复制的子目录列表。

### 3. 过滤机制
可以添加文件类型过滤或排除特定文件的逻辑。

## 总结

这次增强成功实现了：

1. ✅ **选择性复制**：只复制指定的子目录，不复制整个v4l2目录
2. ✅ **声明式配置**：子目录列表在脚本头部以数组形式声明
3. ✅ **健壮的错误处理**：完善的存在性检查和错误恢复机制
4. ✅ **项目根目录参数支持**：修复了基础处理器的参数传递问题
5. ✅ **完整集成测试**：与现有的代码生成流程无缝集成

现在摄像头脚本能够精确地复制指定的子目录，避免了不必要的文件复制，同时保持了良好的可维护性和扩展性！
