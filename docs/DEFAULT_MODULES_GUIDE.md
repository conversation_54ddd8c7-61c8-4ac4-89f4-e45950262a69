# OpenHarmony 设备代码生成器 - 默认模块功能说明

## 📋 概述

从现在开始，以下模块被设定为**默认模块**，无需在YAML配置文件中显式声明，系统会自动生成：

- `build_config` - 构建配置文件
- `distributedhardware` - 分布式硬件组件
- `cfg` - 配置文件（init、fstab等）
- `loader` - 加载器文件

## 🎯 功能特点

### ✅ 自动生成
这些默认模块会在每次代码生成时自动包含，无需在配置文件中声明。

### ✅ 向后兼容
如果你的现有配置文件中包含了这些模块的配置，系统仍然会正常工作，并使用你提供的配置。

### ✅ 简化配置
新的配置文件可以更加简洁，只需要声明需要特殊配置的模块。

## 📝 使用示例

### 简化前的配置（仍然支持）
```yaml
board_code: 
  - kernel:
    linux_kernel_version: 6.1
  - audio_drivers:
      enabled: true
      codec_chip: rk809
  - cfg:
      enabled: true
      init_configs: true
  - distributedhardware:
      enabled: true
  - loader:
      enabled: true
  - build_config:
      enabled: true
```

### 简化后的配置（推荐）
```yaml
board_code: 
  - kernel:
    linux_kernel_version: 6.1
  - audio_drivers:
      enabled: true
      codec_chip: rk809
  # cfg, distributedhardware, loader, build_config 自动生成
```

## 🔧 默认模块详情

### 1. build_config
- **功能**: 生成构建配置文件
- **生成文件**: 
  - `BUILD.gn` - 主构建文件
  - `device.gni` - 设备配置文件
  - `ohos.build` - OpenHarmony构建配置
- **位置**: `board/` 目录下

### 2. cfg
- **功能**: 系统配置文件
- **生成文件**: 
  - `BUILD.gn` - 构建文件
- **位置**: `board/cfg/` 目录下
- **用途**: init配置、fstab配置、USB配置等

### 3. distributedhardware
- **功能**: 分布式硬件组件
- **生成文件**: 
  - `BUILD.gn` - 构建文件
- **位置**: `board/distributedhardware/` 目录下
- **用途**: 分布式硬件组件配置、dinput白名单等

### 4. loader
- **功能**: 加载器文件目录
- **生成文件**: 仅创建目录（不生成BUILD.gn）
- **位置**: `board/loader/` 目录下
- **用途**: 存放加载器相关文件

## 📊 生成结果对比

### 使用默认模块前
```
启用的模块: ['kernel', 'audio_drivers', 'camera_drivers']
生成文件: 32 个
```

### 使用默认模块后
```
启用的模块: ['kernel', 'audio_drivers', 'camera_drivers', 'cfg', 'distributedhardware', 'loader', 'build_config']
生成文件: 37 个
```

## 🎨 自定义默认模块

如果你需要为默认模块提供特殊配置，仍然可以在配置文件中声明：

```yaml
board_code:
  - cfg:
      enabled: true
      init_configs: true
      fstab_config: true
      usb_config: true
  - distributedhardware:
      enabled: true
      components_config: true
      dinput_whitelist: true
  - build_config:
      enabled: true
      build_gn: true
      device_gni: true
      ohos_build: true
```

## 🚀 迁移指南

### 对于新项目
直接使用简化的配置格式，只声明需要特殊配置的模块。

### 对于现有项目
1. **无需修改**: 现有配置文件继续正常工作
2. **可选简化**: 可以移除默认模块的声明来简化配置
3. **渐进迁移**: 可以逐步简化配置文件

## 📈 优势

### 🎯 简化配置
- 减少配置文件的冗余内容
- 降低配置错误的可能性
- 提高配置文件的可读性

### 🔧 标准化
- 确保所有项目都包含必要的基础模块
- 统一项目结构
- 减少遗漏重要模块的风险

### ⚡ 提高效率
- 减少配置时间
- 降低学习成本
- 提高开发效率

## 🔍 技术实现

### 模块识别
系统在处理模块时会自动识别默认模块：
```python
default_modules = ['cfg', 'distributedhardware', 'loader', 'build_config']
```

### 自动添加
在获取启用模块列表时，系统会自动添加默认模块：
```python
for default_module in default_modules:
    if default_module not in enabled_modules:
        enabled_modules.append(default_module)
```

### 智能处理
对于默认模块，即使没有配置也会使用通用处理器进行处理：
```python
is_default_module = module_name in ['cfg', 'distributedhardware', 'loader', 'build_config']
should_use_generic = module_config.get("enabled", is_default_module)
```

## 📋 注意事项

1. **loader模块特殊性**: loader模块只创建目录，不生成BUILD.gn文件
2. **配置优先级**: 如果配置文件中显式声明了默认模块，会使用配置文件中的设置
3. **向后兼容**: 所有现有配置文件都能正常工作，无需修改

这个功能让OpenHarmony设备代码生成器更加智能和易用，同时保持了完全的向后兼容性！
