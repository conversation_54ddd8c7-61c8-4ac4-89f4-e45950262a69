# Copyright (c) 2022-2023 <PERSON><PERSON>hong DID Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("//drivers/hdf_core/adapter/uhdf2/uhdf.gni")

ohos_shared_library("libcodec_oem_interface") {
  include_dirs = [
    "include",
    "//drivers/peripheral/codec/interfaces/include",
    "//device/soc/rockchip/rk3588s/hardware/mpp/include",
    "//device/soc/rockchip/rk3588s/hardware/rga/include",
  ]
  sources = [
    "src/hdi_mpp.c",
    "src/hdi_mpp_component_manager.cpp",
    "src/hdi_mpp_config.c",
    "src/hdi_mpp_mpi.c",
  ]

  deps = [
    "$hdf_uhdf_path/utils:libhdf_utils",
    "//device/soc/{{ soc_company }}/{{ soc }}/hardware/rga:librga",
  ]

  cflags_c = [
    "-Wno-format",
    "-Wno-unused-parameter",
    "-Wno-unused-function",
    "-Wno-implicit-function-declaration",
    "-Wno-incompatible-pointer-types",
    "-Wno-int-conversion",
    "-Wno-macro-redefined",
  ]

  if (is_standard_system) {
    external_deps = [
      "c_utils:utils",
      "drivers_peripheral_display:hdi_gralloc_client",
      "graphic_surface:buffer_handle",
      "hilog:libhilog",
      "ipc:ipc_single",
    ]
  } else {
    external_deps = [ "hilog:libhilog" ]
  }

  install_images = [ chipset_base_dir ]
  subsystem_name = "hdf"
  part_name = "rockchip_products"
}

group("codec_oem_interface") {
  deps = [
    ":libcodec_oem_interface",
    "jpeg:libjpeg_vdi_impl",
  ]
}
