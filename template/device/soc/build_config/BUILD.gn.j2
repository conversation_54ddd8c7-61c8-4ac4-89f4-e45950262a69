# Copyright (C) 2021-2023 HiHope Open Source Organization .
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

group("hardware_group") {
  deps = [
    "//device/soc/rockchip/rk3568/hardware/gpu:mali-bifrost-g52-g7p0-ohos",
    "//device/soc/rockchip/rk3568/hardware/isp:isp",
    "//device/soc/rockchip/rk3568/hardware/mpp:mpp",
    "//device/soc/rockchip/rk3568/hardware/wifi:ap6xxx",
  ]
}
