# <AUTHOR> <EMAIL>
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

soc_company = "{{ soc_company }}"
soc_name = "{{ soc }}"

import("//device/soc/{{ soc_company }}/{{ soc }}/soc.gni")

import("//build/ohos.gni")
if (!defined(defines)) {
  defines = []
}

declare_args() {
  is_support_boot_animation = true
  is_support_graphic = true
  is_support_codec = true
}

if (!defined(global_parts_info.graphic_graphic_2d)) {
  is_support_graphic = false
}

if (!defined(global_parts_info.hdf_drivers_peripheral_codec)) {
  is_support_codec = false
}

product_config_path = "//vendor/${product_company}/${product_name}"
board_camera_path =
    "//device/board/${product_company}/${device_name}/camera/vdi_impl/v4l2"
is_support_v4l2 = true
if (is_support_v4l2) {
  is_support_mpi = false
  defines += [ "SUPPORT_V4L2" ]
}
