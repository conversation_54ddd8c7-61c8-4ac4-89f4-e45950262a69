# <AUTHOR> <EMAIL>
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("device.gni")

print("{{ product_name }}_group in")
group("{{ product_name }}_group") {
  deps = [
    "cfg:init_configs",
    "distributedhardware:distributedhardware",
    # "hardware:hardware",
    "kernel:kernel",
    "updater:updater_files",
    "//device/soc/{{ soc_company }}/{{ soc }}/hardware:hardware_group",
    # "//third_party/alsa-utils:alsa-utils",
    {% for module in modules_with_build_gn %}
    "{{ module }}:{{ module }}",
    {% endfor %}
  ]

  if (is_support_graphic) {
    deps += [
      "//device/soc/{{ soc_company }}/{{ soc }}/hardware/display:display_buffer_model",
      "//device/soc/{{ soc_company }}/{{ soc }}/hardware/display:display_composer_model",
    ]
  }

  if (is_support_codec) {
    deps += [
      "//device/soc/{{ soc_company }}/{{ soc }}/hardware/codec:codec_oem_interface",
      "//device/soc/{{ soc_company }}/{{ soc }}/hardware/omx_il:lib_omx",
    ]
  }
  if (is_support_boot_animation) {
    deps += [ "bootanimation:bootanimation" ]
  }
  # if (is_standard_system) {
  #   deps += [ "startup/reboot_loader:rebootloader" ]
  # }
}
