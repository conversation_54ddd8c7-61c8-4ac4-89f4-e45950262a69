## CFG Configuration Module

### Configuration Information
- **Init Configuration**: {{ 'Enabled' if module_config.get('init_configs', false) else 'Disabled' }}
- **Fstab Configuration**: {{ 'Enabled' if module_config.get('fstab_config', false) else 'Disabled' }}
- **USB Configuration**: {{ 'Enabled' if module_config.get('usb_config', false) else 'Disabled' }}

### Developer Tasks:

#### 1. System Configuration File Verification
{% if module_config.get('init_configs', false) %}
- **Check init.cfg startup configuration**
  ```bash
  # Verify generated init configuration files
  cat output/*/{{ product_name | default('product') }}/board/cfg/init.*.cfg
  ```
  
  **Configuration items to check**:
  - System service startup order
  - Permission and user group settings
  - Environment variable configuration
  - System property settings

  **Common configuration example**:
  ```json
  {
      "services" : [{
          "name" : "your_service",
          "path" : ["/system/bin/your_service"],
          "uid" : "system",
          "gid" : ["system", "shell"],
          "start-mode" : "boot"
      }]
  }
  ```

{% endif %}

{% if module_config.get('fstab_config', false) %}
- **Verify fstab filesystem mount configuration**
  ```bash
  # Check fstab configuration files
  cat output/*/{{ product_name | default('product') }}/board/cfg/fstab.*
  ```
  
  **Mount points to verify**:
  - System partition mounts (`/system`, `/vendor`, `/data`)
  - Storage device mounts (`/sdcard`, `/storage`)
  - Special filesystems (`/proc`, `/sys`, `/dev`)

  **Configuration format example**:
  ```
  # <source>  <mount_point>  <type>  <mount_options>  <fs_mgr_flags>
  /dev/block/mmcblk0p5 /vendor ext4 ro,barrier=1 wait,avb=vbmeta_vendor
  /dev/block/mmcblk0p6 /data ext4 noatime,nosuid,nodev,noauto_da_alloc wait,check,quota
  ```

{% endif %}

{% if module_config.get('usb_config', false) %}
- **Configure USB related parameters**
  ```bash
  # Check USB configuration
  cat output/*/{{ product_name | default('product') }}/board/cfg/usb_*
  ```
  
  **USB configuration items**:
  - USB device mode (device/host/otg)
  - USB function configuration (adb, mtp, ptp)
  - USB permission settings

{% endif %}

#### 2. Boot Process Verification
- **Test system boot process**
  ```bash
  # Check boot logs
  dmesg | grep -i init
  
  # View service status
  ps -ef | grep your_service
  ```

- **Verify service startup order**
  - Confirm critical services start in correct order
  - Check service dependencies
  - Verify startup time and performance

#### 3. Permission and Security Configuration
- **Verify user and group configuration**
  ```bash
  # Check user configuration
  cat /etc/passwd
  cat /etc/group
  ```

- **Check file permissions**
  ```bash
  # Verify critical file permissions
  ls -la /system/etc/
  ls -la /vendor/etc/
  ```

#### 4. System Property Configuration
- **Verify system property settings**
  ```bash
  # View system properties
  getprop | grep ro.product
  getprop | grep ro.build
  ```

- **Custom property configuration**
  - Add product-specific properties in `build.prop`
  - Configure hardware-related properties
  - Set debug and development properties

### Configuration File Description

#### Init Configuration Files
- **Location**: `device/{{ device_company | default('company') }}/{{ product_name | default('product') }}/board/cfg/`
- **File**: `init.{{ product_name | default('product') }}.cfg`
- **Purpose**: Define services and configurations during system startup

#### Fstab Configuration Files
- **Location**: `device/{{ device_company | default('company') }}/{{ product_name | default('product') }}/board/cfg/`
- **File**: `fstab.{{ product_name | default('product') }}`
- **Purpose**: Define filesystem mount configuration

#### USB Configuration Files
- **Location**: `device/{{ device_company | default('company') }}/{{ product_name | default('product') }}/board/cfg/`
- **File**: `usb_*.cfg`
- **Purpose**: Define USB functions and permissions

### Common Troubleshooting

#### Boot Failure
```bash
# Check boot logs
dmesg | tail -100

# View init process status
ps aux | grep init
```

#### Service Startup Failure
- Check service configuration file syntax
- Verify executable file path
- Confirm permission settings are correct

#### Filesystem Mount Failure
- Check partition table configuration
- Verify filesystem format
- Confirm mount point permissions

### Testing and Verification
```bash
# Complete boot test
reboot

# Check all service status
systemctl status --all

# Verify filesystem mounts
mount | grep -E "(system|vendor|data)"
```
