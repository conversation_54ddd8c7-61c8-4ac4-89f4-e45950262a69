# Copyright (C) 2021 HiHope Open Source Organization .
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

ohos_prebuilt_etc("init.{{ soc }}.cfg") {
  source = "init.{{ soc }}.cfg"
  part_name = "{{ soc_company }}_products"
  install_images = [ chipset_base_dir ]
  install_enable = true
}

ohos_prebuilt_etc("init.{{ soc }}.usb.cfg") {
  source = "init.{{ soc }}.usb.cfg"
  part_name = "{{ soc_company }}_products"
  install_images = [ chipset_base_dir ]
  install_enable = true
}

ohos_prebuilt_etc("fstab.{{ soc }}") {
  source = "fstab.{{ soc }}"
  part_name = "{{ soc_company }}_products"
  install_images = [ chipset_base_dir ]
  install_enable = true
}

group("init_configs") {
  deps = [
    ":fstab.{{ soc }}",
    ":init.{{ soc }}.cfg",
    ":init.{{ soc }}.usb.cfg",
  ]
}
