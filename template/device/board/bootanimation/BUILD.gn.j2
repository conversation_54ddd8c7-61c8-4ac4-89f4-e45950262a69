# Copyright (C) 2021 HiHope Open Source Organization .
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import("//build/ohos.gni")

BOOTANIMATION_DIR = "//device/board/hihope/${device_name}/bootanimation"

ohos_prebuilt_etc("bootanimation-720x1280") {
  source = "$BOOTANIMATION_DIR/bootanimation-720x1280.raw"
  part_name = "{{ soc_company }}_products"
  install_enable = true
}

ohos_prebuilt_etc("bootanimation-1920x1080") {
  source = "$BOOTANIMATION_DIR/bootanimation-1920x1080.raw"
  part_name = "{{ soc_company }}_products"
  install_enable = true
}

group("bootanimation") {
  deps = [
    ":bootanimation-1920x1080",
    ":bootanimation-720x1280",
  ]
}
