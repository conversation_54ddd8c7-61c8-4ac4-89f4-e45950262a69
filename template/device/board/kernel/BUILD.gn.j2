# <AUTHOR> <EMAIL>
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/config/clang/clang.gni")
import("//build/ohos.gni")
kernel_build_script_dir = "{{ linux_kernel_source_path }}"
kernel_source_dir = "{{ linux_kernel_source_path }}"

action("kernel") {
  script = "build_kernel.sh"
  sources = [ kernel_source_dir ]

  product_path = "vendor/$product_company/$product_name"
  outputs = [ "$root_build_dir/../kernel/src_tmp/{{ linux_kernel_version }}/boot_linux" ]
  args = [
    rebase_path(kernel_build_script_dir, root_build_dir),
    rebase_path("$root_build_dir/packages/phone/images"),
    rebase_path("//device/board/{{ device_company }}/$product_name"),
    product_path,
    rebase_path("$root_build_dir/../.."),
    device_company,
    device_name,
    product_company,
  ]
  if (enable_ramdisk) {
    args += [ "enable_ramdisk" ]
  }
}
