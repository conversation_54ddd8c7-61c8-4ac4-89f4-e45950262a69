#!/bin/bash

# <AUTHOR> <EMAIL>
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

set -e



pushd ${1}
ROOT_DIR=${5}
export PRODUCT_PATH=${4}
export DEVICE_COMPANY=${6}
export DEVICE_NAME=${7}
export PRODUCT_COMPANY=${8}

KERNEL_SRC_TMP_PATH=${ROOT_DIR}/out/kernel/src_tmp/{{ linux_kernel_version }}
KERNEL_OBJ_TMP_PATH=${ROOT_DIR}/out/kernel/OBJ/{{ linux_kernel_version }}
KERNEL_SOURCE=${ROOT_DIR}/kernel/linux/{{ linux_kernel_version }}
KERNEL_PATCH_PATH={{ kernel_patch_dir }}
KERNEL_PATCH={{ kernel_patch_path }}
HDF_PATCH={{ hdf_patch_path }}
KERNEL_CONFIG_FILE=${ROOT_DIR}/device/board/{{ device_company }}/{{ product_name }}/kernel/config/{{ soc }}_standard_defconfig

rm -rf ${KERNEL_SRC_TMP_PATH}
mkdir -p ${KERNEL_SRC_TMP_PATH}

rm -rf ${KERNEL_OBJ_TMP_PATH}
mkdir -p ${KERNEL_OBJ_TMP_PATH}

export KBUILD_OUTPUT=${KERNEL_OBJ_TMP_PATH}

echo "Copy kernel source"
cp -arf ${KERNEL_SOURCE}/* ${KERNEL_SRC_TMP_PATH}/

cd ${KERNEL_SRC_TMP_PATH}

{% if if_use_kernel_patch==true %}
#HDF patch
echo "HDF patch"
bash ${ROOT_DIR}/drivers/hdf_core/adapter/khdf/linux/patch_hdf.sh ${ROOT_DIR} ${KERNEL_SRC_TMP_PATH} ${KERNEL_PATCH_PATH} ${DEVICE_NAME}

#kernel patch
echo "kernel patch"
patch -p1 < ${KERNEL_PATCH}  
{% else %}
#hdf
echo "Patch hdf"
bash ${ROOT_DIR}/device/board/{{ device_company }}/{{ product_name }}/kernel/patch_hdf.sh ${ROOT_DIR} ${KERNEL_SRC_TMP_PATH}
{% endif %}


chmod +x ${KERNEL_SRC_TMP_PATH}/scripts/mk*

if [ -n "$(shopt -s nullglob; ls ${3}/kernel/logo*)" ]; then
    cp -rf ${3}/kernel/logo* ${KERNEL_SRC_TMP_PATH}/
fi
if [ -n "$(shopt -s nullglob; ls ${3}/kernel/logo*)" ]; then
    cp -rf ${3}/kernel/make*.sh ${KERNEL_SRC_TMP_PATH}/
fi

cp -rf ${3}/kernel/drivers/dts/* ${KERNEL_SRC_TMP_PATH}/arch/arm64/boot/dts/rockchip

#config
cp -rf ${KERNEL_CONFIG_FILE} ${KERNEL_SRC_TMP_PATH}/arch/arm64/configs/{{ soc_company }}_linux_defconfig

if [ "enable_ramdisk" == "${9}" ]; then
    ./make-ohos.sh {{ product_name }} enable_ramdisk
else
    ./make-ohos.sh {{ product_name }} disable_ramdisk
fi

mkdir -p ${2}
# copy the kernel image and modules
# you can add other files you need
if [ -n "$(shopt -s nullglob; ls ${3}/kernel/logo*)" ]; then
    cp ${KERNEL_OBJ_TMP_PATH}/resource.img ${2}/resource.img/
fi
cp ${3}/loader/MiniLoaderAll.bin ${2}/MiniLoaderAll.bin
if [ -n "$(shopt -s nullglob; ls ${3}/kernel/logo*)" ]; then
    cp ${3}/loader/misc.img ${2}/misc.img/
fi
cp ${3}/loader/uboot.img ${2}/uboot.img
if [ "enable_absystem" == "${15}" ]; then
    cp ${3}/loader/parameter_ab.txt ${2}/parameter_ab.txt
    cp ${3}/loader/config_ab.cfg ${2}/config_ab.cfg
else
    cp ${3}/loader/parameter.txt ${2}/parameter.txt
    cp ${3}/loader/config.cfg ${2}/config.cfg
fi


popd

../kernel/src_tmp/{{ linux_kernel_version }}/make-boot.sh ..
