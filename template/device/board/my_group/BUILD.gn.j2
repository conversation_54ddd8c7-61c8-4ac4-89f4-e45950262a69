# Copyright (c) {{ year | default('2024') }} {{ device_company }}
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

# {{ module_description | default(module_name + ' module') }}
# This is a generic module template for {{ module_name }}
# Please add your specific implementation here

# Example shared library (uncomment and modify as needed):
# ohos_shared_library("{{ module_name }}_lib") {
#   sources = [ "{{ module_name }}.c" ]
#   part_name = "{{ soc_company }}_products"
#   install_enable = true
#   install_images = [ "system" ]
# }

# Example prebuilt etc file (uncomment and modify as needed):
# ohos_prebuilt_etc("{{ module_name }}_config") {
#   source = "config/{{ module_name }}.cfg"
#   module_install_dir = "etc/{{ module_name }}"
#   install_images = [ "system" ]
#   part_name = "{{ soc_company }}_products"
# }

# Main group for {{ module_name }} module
group("{{ module_name }}") {
  # Add your dependencies here
  # deps = [
  #   ":{{ module_name }}_lib",
  #   ":{{ module_name }}_config",
  # ]
}