## {{ module_name.title() }} Module

### Configuration Information
- **Module Type**: {{ module_type }}
- **Description**: {{ description }}
- **Build File**: {{ 'BUILD.gn generated' if needs_build_gn else 'No BUILD.gn needed' }}
{% if module_config.get('enabled') is defined %}
- **Enable Status**: {{ 'Enabled' if module_config.get('enabled', false) else 'Disabled' }}
{% endif %}

### Developer Tasks:

#### 1. Check Generated Files
- **Verify all files are correctly generated**
  ```bash
  # Check module directory
  ls -la output/*/{{ product_name | default('product') }}/board/{{ module_name }}/
  
  {% if needs_build_gn %}
  # Verify BUILD.gn file
  cat output/*/{{ product_name | default('product') }}/board/{{ module_name }}/BUILD.gn
  {% endif %}
  ```

- **Check configuration meets expectations**
  - Confirm module configuration parameters are correct
  - Verify dependency relationship settings

#### 2. Complete Module Implementation
{% if needs_build_gn %}
- **Add specific implementation in generated BUILD.gn**
  ```gn
  # Add source files in BUILD.gn
  sources = [
    "src/{{ module_name }}_impl.cpp",
    "src/{{ module_name }}_utils.cpp",
  ]
  
  # Add header file paths
  include_dirs = [
    "include",
    "//path/to/dependencies",
  ]
  
  # Add dependency libraries
  deps = [
    "//foundation/communication/ipc/interfaces/innerkits/ipc_core:ipc_core",
    # Add other necessary dependencies
  ]
  ```

- **Write code according to {{ module_name }} module requirements**
  - Implement core functionality logic
  - Add error handling and logging
  - Write unit tests

{% else %}
- **Manually manage files and dependencies**
  - This module does not use BUILD.gn build system
  - Need to manually add required files
  - Configure custom build process

{% endif %}

#### 3. Dependency Configuration
{% if needs_build_gn %}
- **Add necessary dependency libraries**
  - Analyze module functionality requirements
  - Add system library dependencies
  - Configure third-party library dependencies

- **Configure compilation options**
  ```gn
  # Add compilation flags
  cflags = [
    "-Wall",
    "-Werror",
    "-O2",
  ]
  
  # Add preprocessor macros
  defines = [
    "{{ module_name.upper() }}_ENABLED",
    "LOG_TAG=\"{{ module_name }}\"",
  ]
  ```

{% else %}
- **Configure module-specific build method**
  - Choose build tools according to module characteristics
  - Set compilation and linking parameters
  - Configure installation and deployment process

{% endif %}

#### 4. Testing and Verification
- **Write unit tests**
  ```cpp
  // Example test code
  #include "gtest/gtest.h"
  #include "{{ module_name }}_impl.h"
  
  class {{ module_name.title() }}Test : public testing::Test {
  protected:
      void SetUp() override {
          // Test initialization
      }
  };
  
  TEST_F({{ module_name.title() }}Test, BasicFunctionality) {
      // Test basic functionality
      EXPECT_TRUE(true);
  }
  ```

- **Integration testing**
  - Test module integration with other system parts
  - Verify interface compatibility
  - Check performance metrics

#### 5. Documentation and Maintenance
- **Write module documentation**
  - API interface documentation
  - Usage instructions and examples
  - Troubleshooting guide

- **Code review and optimization**
  - Conduct code review
  - Performance optimization
  - Security checks

### Common Troubleshooting

#### Compilation Errors
```bash
# Check if dependencies are correct
ninja -C out/rk3568 {{ module_name }}

# View detailed compilation information
ninja -C out/rk3568 -v {{ module_name }}
```

#### Runtime Errors
- Check log output
- Verify configuration files
- Confirm permission settings

### Related File Locations
- **Module Source Code**: `device/{{ device_company | default('company') }}/{{ product_name | default('product') }}/board/{{ module_name }}/`
- **Configuration Files**: `device/{{ device_company | default('company') }}/{{ product_name | default('product') }}/etc/`
- **Build Configuration**: `device/{{ device_company | default('company') }}/{{ product_name | default('product') }}/board/BUILD.gn`

### Reference Materials
- [OpenHarmony Device Development Guide](https://docs.openharmony.cn/pages/v4.0/en/device-dev/device-dev-guide.md)
- [BUILD.gn Syntax Reference](https://docs.openharmony.cn/pages/v4.0/en/device-dev/subsystems/subsys-build-gn-coding-style-and-best-practice.md)

*Note: This module uses default report template, recommend creating custom report.md.j2 file*
