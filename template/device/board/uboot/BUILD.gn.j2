# <AUTHOR> <EMAIL>
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/config/clang/clang.gni")
import("//build/ohos.gni")
uboot_build_script_dir = "//device/board/{{ device_company }}/{{ product_name }}/uboot/u-boot-{{ device_company }}"
uboot_source_dir = "//device/board/{{ device_company }}/{{ product_name }}/uboot/u-boot-{{ device_company }}"

action("uboot") {
  script = "build_uboot.sh"
  sources = [ uboot_source_dir ]

  outputs = [ "$root_build_dir/../uboot/src_tmp/uboot" ]
  args = [
    rebase_path(uboot_build_script_dir, root_build_dir),
    rebase_path("$root_build_dir/packages/phone/images"),
    rebase_path("$root_build_dir/../.."),
    rebase_path("//device/board/{{ device_company }}/$product_name"),
  ]
}