# Copyright (C) 2022 HiHope Open Source Organization .
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import("//build/ohos.gni")

ohos_prebuilt_etc("distributed_hardware_components_cfg.json") {
  source = "distributed_hardware_components_cfg.json"
  install_enable = true
  module_install_dir = "etc/distributedhardware/"
  install_images = [ chipset_base_dir ]
  part_name = "{{ soc_company }}_products"
}

ohos_prebuilt_etc("dinput_business_event_whitelist.cfg") {
  source = "dinput_business_event_whitelist.cfg"
  install_enable = true
  module_install_dir = "etc/distributedhardware/"
  install_images = [ chipset_base_dir ]
  part_name = "{{ soc_company }}_products"
}

group("distributedhardware") {
  deps = [
    ":dinput_business_event_whitelist.cfg",
    ":distributed_hardware_components_cfg.json",
  ]
}
