## Camera Module

### Configuration Information
- **Camera Chip**: {{ module_config.get('camera_chip', 'unknown') }}
- **Interface Method**: {{ module_config.get('method', 'v4l2') }}
- **Node Count**: {{ module_config.get('node', []) | length }}
{% if module_config.get('node', []) %}
- **Node List**: {{ module_config.get('node', []) | join(', ') }}
{% endif %}

### Developer Tasks:

#### 1. Hardware Driver Verification
- **Confirm {{ module_config.get('camera_chip', 'unknown') }} driver is loaded**
  ```bash
  # Check driver module
  lsmod | grep {{ module_config.get('camera_chip', 'unknown').lower() }}
  
  # Check device nodes
  ls -la /dev/video*
  ```

- **Verify {{ module_config.get('method', 'v4l2').upper() }} interface is working properly**
  ```bash
  # View camera device information
  v4l2-ctl --list-devices
  
  # Check supported formats
  v4l2-ctl --list-formats-ext
  ```

#### 2. Device Node Configuration
- **Check /dev/video* device nodes**
  - Confirm device node permissions are correctly set
  - Verify device node to hardware correspondence

- **Configure camera permissions**
  ```xml
  <!-- Add to device/xxx/xxx/etc/permissions/xxx.xml -->
  <permission name="ohos.permission.CAMERA" />
  ```

#### 3. Image Processing Pipeline
{% if module_config.get('node', []) %}
- **Custom Node Implementation**:
{% for node in module_config.get('node', []) %}
  - **{{ node }}_node**: Implement specific {{ node }} processing logic
    - Find `{{ node }}_node.cpp` in generated code
    - Implement image processing algorithms based on hardware characteristics
    - Configure processing parameters and performance optimization
{% endfor %}

- **Node Connection Configuration**
  - Configure data flow between nodes
  - Set buffer size and format
  - Optimize processing performance

{% else %}
- **Use Default Image Processing Pipeline**
  - Check if default configuration meets requirements
  - Adjust image quality parameters as needed

{% endif %}

#### 4. Functional Testing
- **Test camera preview functionality**
  ```bash
  # Test preview using gstreamer
  gst-launch-1.0 v4l2src device=/dev/video0 ! videoconvert ! ximagesink
  ```

- **Verify photo and video recording functions**
  ```bash
  # Photo test
  v4l2-ctl --device=/dev/video0 --stream-mmap --stream-count=1 --stream-to=test.raw
  
  # Video recording test
  ffmpeg -f v4l2 -i /dev/video0 -t 10 test.mp4
  ```

- **Check image quality and performance**
  - Verify image resolution and frame rate
  - Check color reproduction and clarity
  - Test performance under different lighting conditions

#### 5. System Integration
- **Integrate with Camera Service**
  - Configure camera device information
  - Implement Camera HAL interface
  - Register camera device to system

- **Permission and Security Configuration**
  - Configure camera access permissions
  - Set security policies
  - Implement privacy protection features

### Common Troubleshooting

#### Camera Device Not Recognized
```bash
# Check hardware connection
dmesg | grep -i camera

# Check device tree configuration
cat /proc/device-tree/camera*/status
```

#### Image Quality Issues
- Adjust exposure and gain parameters
- Configure white balance settings
- Optimize image processing algorithms

#### Performance Issues
- Adjust buffer size
- Optimize memory allocation
- Use hardware acceleration features

### Configuration File Locations
- **Device Configuration**: `device/{{ device_company | default('company') }}/{{ product_name | default('product') }}/camera/`
- **Permission Configuration**: `device/{{ device_company | default('company') }}/{{ product_name | default('product') }}/etc/permissions/`
- **HAL Implementation**: `drivers/peripheral/camera/`
