# Copyright (c) 2021 - 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import("//build/ohos.gni")
import("//device/board/${product_company}/${device_name}/device.gni")
import("//drivers/peripheral/camera/camera.gni")

config("device_manager_config") {
  visibility = [ ":*" ]

  cflags_cc = [
    "-Wall",
    "-Wextra",
    "-Werror",
    "-Wno-error",
    "-DGST_DISABLE_DEPRECATED",
    "-DHAVE_CONFIG_H",
    "-DCOLORSPACE=\"videoconvert\"",
    "-fno-strict-aliasing",
    "-Wno-sign-compare",
    "-Wno-builtin-requires-header",
    "-Wno-unused-variable",
    "-Wno-unused-label",
    "-Wno-implicit-function-declaration",
    "-Wno-format",
    "-Wno-int-conversion",
    "-Wno-unused-function",
    "-Wno-thread-safety-attributes",
    "-Wno-inconsistent-missing-override",
    "-fno-rtti",
    "-fno-exceptions",
    "-ffunction-sections",
    "-fdata-sections",
  ]
}

ohos_shared_library("camera_device_manager") {
  sources = [
    "$camera_path/adapter/platform/v4l2/src/device_manager/idevice_manager.cpp",
    "$camera_path/adapter/platform/v4l2/src/device_manager/v4l2_device_manager.cpp",
    "src/{{ sensor }}.cpp",
  ]

  include_dirs = [
    "//base/hiviewdfx/interfaces/innerkits/libhilog/include",
    "./include",
    "//commonlibrary/c_utils/base/include",
  ]

  deps = []

  if (is_standard_system) {
    external_deps = [
      "c_utils:utils",
      "drivers_peripheral_camera:peripheral_camera_device_manager",
      "drivers_peripheral_camera:peripheral_camera_v4l2_adapter",
      "hdf_core:libhdf_utils",
      "hilog:libhilog",
    ]
  } else {
    external_deps = [
      "c_utils:utils",
      "hilog:libhilog",
    ]
  }
  external_deps += [ "drivers_interface_camera:metadata" ]
  public_configs = [ ":device_manager_config" ]
  install_images = [ chipset_base_dir ]
  subsystem_name = "rockchip_products"
  part_name = "rockchip_products"
}
