/*
 * Copyright (c) {{ year }} Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *     http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "my_{{ node_name }}_node.h"
#include <securec.h>
#include "camera_dump.h"
// Add your {{ node_name }} node specific includes here

namespace OHOS::Camera {

My{{ node_name|title }}Node::My{{ node_name|title }}Node(const std::string& name, const std::string& type, const std::string &cameraId)
    : NodeBase(name, type, cameraId)
{
    CAMERA_LOGV("%{public}s enter, type(%{public}s)\n", name_.c_str(), type_.c_str());
    // Add your {{ node_name }} node specific initialization here
}

My{{ node_name|title }}Node::~My{{ node_name|title }}Node()
{
    CAMERA_LOGI("~My{{ node_name|title }}Node Node exit.");
}

RetCode My{{ node_name|title }}Node::Start(const int32_t streamId)
{
    CAMERA_LOGI("My{{ node_name|title }}Node::Start streamId = %{public}d\n", streamId);
    // Add your {{ node_name }} node specific start logic here
    return RC_OK;
}

RetCode My{{ node_name|title }}Node::Stop(const int32_t streamId)
{
    CAMERA_LOGI("My{{ node_name|title }}Node::Stop streamId = %{public}d\n", streamId);
    // Add your {{ node_name }} node specific stop logic here
    return RC_OK;
}

RetCode My{{ node_name|title }}Node::Flush(const int32_t streamId)
{
    CAMERA_LOGI("My{{ node_name|title }}Node::Flush streamId = %{public}d\n", streamId);
    return RC_OK;
}

void My{{ node_name|title }}Node::DeliverBuffer(std::shared_ptr<IBuffer>& buffer)
{
    if (buffer == nullptr) {
        CAMERA_LOGE("My{{ node_name|title }}Node::DeliverBuffer frameSpec is null");
        return;
    }

    // Add your {{ node_name }} node specific buffer processing logic here
    // Example:
    // if (buffer->GetBufferStatus() != CAMERA_BUFFER_STATUS_OK) {
    //     CAMERA_LOGE("Buffer status error");
    //     return NodeBase::DeliverBuffer(buffer);
    // }
    //
    // Process{{ node_name|title }}Buffer(buffer);

    CameraDumper& dumper = CameraDumper::GetInstance();
    // dumper.DumpBuffer("board_My{{ node_name|title }}Node", ENABLE_MY{{ node_name|upper }}_NODE_CONVERTED, buffer);

    return NodeBase::DeliverBuffer(buffer);
}

RetCode My{{ node_name|title }}Node::Config(const int32_t streamId, const CaptureMeta& meta)
{
    // Add your {{ node_name }} node specific configuration logic here
    // Example:
    // if (meta == nullptr) {
    //     CAMERA_LOGE("meta is nullptr");
    //     return RC_ERROR;
    // }
    //
    // return Configure{{ node_name|title }}Parameters(meta);

    (void)streamId;
    (void)meta;
    return RC_OK;
}

RetCode My{{ node_name|title }}Node::Capture(const int32_t streamId, const int32_t captureId)
{
    CAMERA_LOGV("My{{ node_name|title }}Node::Capture");
    return RC_OK;
}

RetCode My{{ node_name|title }}Node::CancelCapture(const int32_t streamId)
{
    CAMERA_LOGI("My{{ node_name|title }}Node::CancelCapture streamid = %{public}d", streamId);
    // Add your {{ node_name }} node specific cancel capture logic here
    return RC_OK;
}

// Add your {{ node_name }} node specific method implementations here
// Example:
// RetCode My{{ node_name|title }}Node::Process{{ node_name|title }}Data(std::shared_ptr<IBuffer>& buffer)
// {
//     // Your {{ node_name }} specific processing logic
//     return RC_OK;
// }

REGISTERNODE(My{{ node_name|title }}Node, {"My{{ node_name|title }}"})
} // namespace OHOS::Camera