/*
 * Copyright (c) {{ year }} Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *     http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef HOS_CAMERA_MY{{ node_name|upper }}_NODE_H
#define HOS_CAMERA_MY{{ node_name|upper }}_NODE_H

#include <vector>
#include <condition_variable>
#include <ctime>
#include <mutex>
#include "device_manager_adapter.h"
#include "utils.h"
#include "camera.h"
#include "source_node.h"

// Add your {{ node_name }} node specific includes here
// Example: #include "your_{{ node_name }}_specific_headers.h"

namespace OHOS::Camera {

class My{{ node_name|title }}Node : public NodeBase {
public:
    My{{ node_name|title }}Node(const std::string& name, const std::string& type, const std::string &cameraId);
    ~My{{ node_name|title }}Node() override;
    RetCode Start(const int32_t streamId) override;
    RetCode Stop(const int32_t streamId) override;
    void DeliverBuffer(std::shared_ptr<IBuffer>& buffer) override;
    virtual RetCode Capture(const int32_t streamId, const int32_t captureId) override;
    RetCode CancelCapture(const int32_t streamId) override;
    RetCode Flush(const int32_t streamId);
    RetCode Config(const int32_t streamId, const CaptureMeta& meta) override;

private:
    // Add your {{ node_name }} node specific private methods here
    // Example:
    // RetCode Process{{ node_name|title }}Data(std::shared_ptr<IBuffer>& buffer);
    // RetCode Handle{{ node_name|title }}Config(const CaptureMeta& meta);

    // Add your {{ node_name }} node specific private members here
    // Example:
    // std::mutex {{ node_name }}Lock_;
    // std::shared_ptr<{{ node_name|title }}Handler> handler_;

    std::mutex nodeLock_;
};
} // namespace OHOS::Camera
#endif