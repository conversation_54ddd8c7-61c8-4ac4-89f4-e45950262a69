/*
 * Copyright (c) {{ year }} Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *     http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __MY_NODE_UTILS_H__
#define __MY_NODE_UTILS_H__

#include "ibuffer.h"

namespace OHOS::Camera {
    /**
     * @brief Utility class for camera node operations
     *
     * This class provides common utility functions that can be used
     * across different camera nodes for buffer processing and format conversion.
     */
    class MyNodeUtils {
    public:
        /**
         * @brief Transform buffer scale and format
         *
         * This function handles buffer scaling and format conversion operations.
         * It can transform the buffer data between different formats and sizes
         * based on the buffer's current and target properties.
         *
         * @param buffer Shared pointer to the buffer that needs transformation
         * @param flagToFd Flag indicating whether to transform to file descriptor (default: true)
         *                 - true: Transform data to file descriptor
         *                 - false: Transform data to virtual address
         *
         * Usage example:
         * MyNodeUtils::BufferScaleFormatTransform(buffer, true);
         *
         * Implementation notes:
         * - Check if transformation is needed by comparing current and target properties
         * - Convert OHOS format to hardware-specific format
         * - Perform the actual transformation using hardware acceleration if available
         * - Update buffer properties after successful transformation
         */
        static void BufferScaleFormatTransform(std::shared_ptr<IBuffer>& buffer, bool flagToFd = true);

        // Add your additional utility methods here
        // Example:
        // /**
        //  * @brief Convert OHOS format to hardware format
        //  * @param ohosFormat The OHOS camera format
        //  * @return Hardware-specific format value
        //  */
        // static uint32_t ConvertOhosFormatToHardwareFormat(uint32_t ohosFormat);

        // /**
        //  * @brief Check if buffer transformation is required
        //  * @param buffer The buffer to check
        //  * @return true if transformation is needed, false otherwise
        //  */
        // static bool IsTransformationRequired(std::shared_ptr<IBuffer>& buffer);
    };
}

#endif