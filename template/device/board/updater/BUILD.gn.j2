# Copyright (c) 2021 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

updater_ueventd_cfg = "//base/startup/init/services/etc/ueventd.cfg"

ohos_prebuilt_etc("updater_ueventd.cfg") {
  source = "${updater_ueventd_cfg}"
  install_images = [ "updater" ]
  part_name = "{{ soc_company }}_products"
}

ohos_prebuilt_etc("updater_init.cfg") {
  source = "config/init.cfg"
  install_images = [ "updater" ]
  part_name = "{{ soc_company }}_products"
}

ohos_prebuilt_etc("updater_init_{{ soc }}_usb.cfg") {
  source = "config/init.{{ soc }}.usb.cfg"
  install_images = [ "updater" ]
  part_name = "{{ soc_company }}_products"
}

ohos_prebuilt_etc("signing_cert.crt") {
  source = "config/signing_cert.crt"
  module_install_dir = "etc/certificate"
  install_images = [
    "system",
    "updater",
  ]
  part_name = "{{ soc_company }}_products"
}

ohos_prebuilt_etc("fstab.updater") {
  source = "config/fstab.updater"
  install_images = [ "updater" ]
  part_name = "{{ soc_company }}_products"
}

group("updater_files") {
  deps = [
    ":fstab.updater",
    ":signing_cert.crt",
    ":updater_init.cfg",
    ":updater_init_{{ soc }}_usb.cfg",
    ":updater_ueventd.cfg",
  ]
}
