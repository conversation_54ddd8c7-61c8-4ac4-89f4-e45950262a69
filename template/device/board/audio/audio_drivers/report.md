## Audio 音频模块

下面文档以rk809_codec为例进行说明，涉及具体编解码芯片名称在代码中已进行替换。
<codec_chip>和<compatible_driver>在yaml中的定义是为了兼容rk3568的设计，如果选用的编解码芯片不存在

ADM框架的音频驱动主要分为codec、dai、soc(dma)、dsp四个部分，分别需要对codec 、dai、soc(dma)部分进行修改
根据hdf框架要求，首先需要在adapter文件中实现具体驱动的注册，而底层驱动的控制由linux内核的驱动框架实现，
因此需要有相应的linux_driver.c文件进行linux设备驱动注册，
impl.c文件则是对于adapter中注册的操作数的具体实现文件。
- codec目录下的文件主要是对于具体codec芯片的操作实现。
- dai目录下的文件主要是对于dai总线的操作实现。
- soc目录下的文件主要是对于soc相关的dma操作实现。
- dsp目录目前没有具体实现，仅有框架，函数内部无实际操作。

### 开发者需要完成的工作:
#### codec部分
参考rk3568的代码实现，codec include中的头文件应按照具体芯片手册内容更改头文件。
例如rk817_codec.h中定义了寄存器地址、寄存器位域等信息，
rk809_codec_impl.h中定义了可能用到的结构体以及rk809_codec_impl.c中函数对外声明。

在rk3568/audio_drivers/codec/rk809_codec/src/rk809_codec_adapter.c中定义了私有数据结构和操作（注册回调函数），完成了hdf驱动节点的注册。
定义了三个私有结构体g_rk809Data，g_rk809DaiDeviceOps和g_rk809DaiData。
```
struct CodecData g_rk809Data = {
    .Init = Rk809DeviceInit,
    .Read = RK809CodecReadReg,
    .Write = Rk809CodecWriteReg,
};

struct AudioDaiOps g_rk809DaiDeviceOps = {
    .Startup = Rk809DaiStartup,
    .HwParams = Rk809DaiHwParams,
    .Trigger = Rk809NormalTrigger,
};

struct DaiData g_rk809DaiData = {
    .DaiInit = Rk809DaiDeviceInit,
    .Read = RK809CodecDaiReadReg,
    .Write = RK809CodecDaiWriteReg,
    .ops = &g_rk809DaiDeviceOps,//指向上面定义的操作数,非函数指针
};
```
这三个结构体涉及到的函数均需要在impl.c中进行具体实现，也就是由开发者完成，总结如下：
```
int32_t Rk809DeviceInit(struct AudioCard *audioCard, const struct CodecDevice *device);
int32_t Rk809DeviceRegRead(uint32_t reg, uint32_t *val);
int32_t Rk809DeviceRegWrite(uint32_t reg, uint32_t value);
int32_t RK809CodecReadReg(const struct CodecDevice *codec, uint32_t reg, uint32_t *val);
int32_t Rk809CodecWriteReg(const struct CodecDevice *codec, uint32_t reg, uint32_t value);
int32_t Rk809RegBitsUpdate(struct AudioMixerControl regAttr);
int32_t Rk809DaiDeviceInit(struct AudioCard *card, const struct DaiDevice *device);
int32_t Rk809DaiStartup(const struct AudioCard *card, const struct DaiDevice *device);
int32_t Rk809DaiHwParams(const struct AudioCard *card, const struct AudioPcmHwParams *param);
int32_t Rk809NormalTrigger(const struct AudioCard *card, int cmd, const struct DaiDevice *device);
int32_t RK809CodecDaiReadReg(const struct DaiDevice *dai, uint32_t reg, uint32_t *value);
int32_t RK809CodecDaiWriteReg(const struct DaiDevice *dai, uint32_t reg, uint32_t value);
```
对于linux驱动的注册在rk809_codec_linux.c中实现，主要是完成了platform驱动的注册。
```
static struct platform_driver rk817_codec_driver = {
    .driver = {
        .name = "rk817-codec",
        .of_match_table = rk817_codec_dt_ids,
    },
    .probe = rk817_platform_probe,//开发者实现
    .remove = rk817_platform_remove,//自主实现
};
```
```
//开发者根据具体芯片举行设置参数
static const struct regmap_config rk817_codec_regmap_config = {
    .name = "rk817-codec",
    .reg_bits = 8,
    .val_bits = 8,
    .reg_stride = 1,
    .max_register = 0x4f,
    .cache_type = REGCACHE_FLAT,
    .volatile_reg = rk817_volatile_register,//开发者实现的函数
    .writeable_reg = rk817_codec_register,//开发者实现的函数
    .readable_reg = rk817_codec_register,//开发者实现的函数
    .reg_defaults = rk817_reg_defaults,//开发者实现的函数
    .num_reg_defaults = ARRAY_SIZE(rk817_reg_defaults),
};

struct regmap_config getCodecRegmap(void)
{
    return rk817_codec_regmap_config;
}
```

注意rk809_codec_adapter.c和rk809_codec_linux_driver.c中头文件引用中还需要包含linux驱动特定芯片的头文件
例如rk809芯片需要包含#include <linux/mfd/rk808.h>
#### dai部分
dai部分与codec部分类似，主要是完成dai的注册和具体实现。
开发者需要在rk3568_dai_ops.c中完成dai的具体实现函数，这些具体实现函数在rk3568_dai_adapter.c中被注册为回调函数。
```
int32_t Rk3568DeviceReadReg(const struct DaiDevice *dai, uint32_t reg, uint32_t *val);
int32_t Rk3568DeviceWriteReg(const struct DaiDevice *dai, uint32_t reg, uint32_t value);

int32_t Rk3568NormalTrigger(const struct AudioCard *card,
    int cmd, const struct DaiDevice *dai);
int32_t Rk3568DaiHwParams(const struct AudioCard *card,
    const struct AudioPcmHwParams *param);
int32_t Rk3568DaiStartup(const struct AudioCard *card,
    const struct DaiDevice *dai);
int32_t Rk3568DaiDeviceInit(struct AudioCard *card,
    const struct DaiDevice *dai);
```
开发者还需要在rk3568_dai_linux_driver.c中注册dai设备的linux驱动节点和
```
static struct platform_driver rockchip_i2s_tdm_driver = {
    .probe = rockchip_i2s_tdm_probe,//开发者需要完成
    .remove = rockchip_i2s_tdm_remove,//开发者需要完成
    .driver = {
        .name = DRV_NAME,
        .of_match_table = of_match_ptr(rockchip_i2s_tdm_match),
        .pm = NULL,
    },
};
```

```
static int rockchip_i2s_tdm_probe(struct platform_device *pdev)
{
   1.内存分配 
   2.读取设备树配置 
   3.获取硬件资源 
   4.驱动数据绑定 
   5.硬件初始配置 
}
```
#### dma部分
soc(dma)部分主要是完成soc相关的dma操作实现，主要是完成dma的注册和具体实现。
rk3568_dma_ops.c：
```
int32_t AudioDmaDeviceInit(const struct AudioCard *card, const struct PlatformDevice *platform);
int32_t Rk3568DmaBufAlloc(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaBufFree(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaRequestChannel(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaConfigChannel(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568PcmPointer(struct PlatformData *data, const enum AudioStreamType streamType, uint32_t *pointer);
int32_t Rk3568DmaPrep(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaSubmit(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaPending(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaPause(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaResume(const struct PlatformData *data, const enum AudioStreamType streamType);
```
