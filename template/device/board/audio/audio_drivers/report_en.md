## Audio Module

{% set adm_config = module_config.get('adm_drivers', {}) %}
{% set alsa_config = module_config.get('alsa_drivers', {}) %}

### Configuration Information
- **ADM Framework**: {{ 'Enabled' if adm_config.get('enable', false) else 'Disabled' }}
- **ALSA Framework**: {{ 'Enabled' if alsa_config.get('enable', false) else 'Disabled' }}

{% if adm_config.get('enable', false) %}
### ADM Framework Configuration
- **Codec Chip**: {{ adm_config.get('codec_chip', 'unknown') }}
- **Compatible Driver**: {{ adm_config.get('compatible_driver', 'N/A') }}
- **Bus Type**: {{ adm_config.get('bus_type', 'N/A') }}

#### Developer Tasks:
1. **Verify Codec Chip Driver**
   - Confirm {{ adm_config.get('codec_chip', 'unknown') }} chip driver is properly integrated
   - Check device tree configuration matches hardware connections

2. **Audio Path Configuration**
   - Configure audio input/output paths
   - Adjust volume control parameters
   - Set audio sampling rate and format

3. **Testing and Verification**
   - Execute audio playback test: `speaker-test -t wav -c 2`
   - Verify recording function: `arecord -f cd test.wav`
   - Check audio quality and latency

{% endif %}

{% if alsa_config.get('enable', false) %}
### ALSA Framework Configuration
- **Codec Chip**: {{ alsa_config.get('codec_chip', 'unknown') }}
- **Implementation Method**: {{ alsa_config.get('implementation_method', 'manual') }}

#### Developer Tasks:
{% if alsa_config.get('implementation_method', 'manual') == 'framework' %}
1. **Framework Mode Configuration**
   - Framework files automatically copied to output directory
   - Check ALSA configuration file `/etc/asound.conf`
   - Verify PCM device configuration

2. **Driver Adaptation**
   - Adapt {{ alsa_config.get('codec_chip', 'unknown') }} chip driver
   - Configure audio device nodes `/dev/snd/`
   - Set correct audio routing

{% else %}
1. **Manual Mode Configuration**
   - Manually configure ALSA driver parameters
   - Implement {{ alsa_config.get('codec_chip', 'unknown') }} chip-specific code
   - Customize audio control interface

2. **Custom Development**
   - Adjust audio parameters according to hardware characteristics
   - Implement special audio features (noise reduction, echo cancellation)
   - Optimize audio performance

{% endif %}
3. **System Integration**
   - Update audio policy configuration files
   - Integrate with AudioManager service
   - Configure audio permissions and security policies

{% endif %}

{% if not adm_config.get('enable', false) and not alsa_config.get('enable', false) %}
### ⚠️ Warning
Audio module is enabled but no specific framework is configured. Please check ADM or ALSA configuration in the config file.

#### Recommended Actions:
1. Enable `adm_drivers` or `alsa_drivers` in configuration file
2. Configure corresponding codec chip information
3. Regenerate code

{% endif %}

### Common Troubleshooting
1. **Audio Device Not Recognized**
   - Check if kernel audio drivers are loaded: `lsmod | grep snd`
   - Verify device tree configuration: `cat /proc/device-tree/...`

2. **No Sound During Audio Playback**
   - Check volume settings: `amixer scontrols`
   - Verify audio path: `amixer contents`

3. **High Audio Latency**
   - Adjust buffer size
   - Optimize audio processing pipeline

