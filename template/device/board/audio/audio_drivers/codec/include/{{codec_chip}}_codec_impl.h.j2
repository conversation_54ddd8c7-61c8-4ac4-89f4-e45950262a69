 /*
 * Copyright (c) 2022 Institute of Software, CAS.
 * Author : h<PERSON><PERSON>@nj.iscas.ac.cn
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef {{ codec_chip.upper() }}_CODEC_IMPL_H
#define {{ codec_chip.upper() }}_CODEC_IMPL_H

#include <linux/platform_device.h>
#include <linux/types.h>
#include "audio_dai_if.h"
#include "audio_codec_if.h"
#include "osal_mem.h"
#include "osal_time.h"
#include "osal_io.h"
#include "securec.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

struct {{ codec_chip | capitalize }}ChipData {
    struct CodecData codec;
    struct DaiData dai;
    struct HdfDeviceObject *hdev;
    struct platform_device *pdev;
    struct regmap *regmap;
};

typedef enum {
    {{ codec_chip.upper() }}_SRT_00 = 0x00,
    {{ codec_chip.upper() }}_SRT_01 = 0x01,
    {{ codec_chip.upper() }}_SRT_02 = 0x02,
    {{ codec_chip.upper() }}_SRT_03 = 0x03,
} {{ codec_chip.upper() }}SampleRateTimes;

typedef enum {
    {{ codec_chip.upper() }}_PREMODE_1 = 0x03,
    {{ codec_chip.upper() }}_PREMODE_2 = 0x06,
    {{ codec_chip.upper() }}_PREMODE_3 = 0x0C,
    {{ codec_chip.upper() }}_PREMODE_4 = 0x18,
} {{ codec_chip.upper() }}PLLInputCLKPreDIV;

typedef enum {
    {{ codec_chip.upper() }}_VDW_16BITS = 0x0F,
    {{ codec_chip.upper() }}_VDW_24BITS = 0x17,
} {{ codec_chip.upper() }}_VDW;

typedef enum {
    UPDATE_LREG = 0,
    UPDATE_RREG = 1,
} Update_Dest;

struct {{ codec_chip.upper() }}DaiParamsVal {
    uint32_t frequencyVal;
    uint32_t DataWidthVal;
};
struct {{ codec_chip | capitalize }}ChipData* GetCodecDevice(void);
int32_t {{ codec_chip | capitalize }}DeviceInit(struct AudioCard *audioCard, const struct CodecDevice *device);
int32_t {{ codec_chip | capitalize }}DeviceRegRead(uint32_t reg, uint32_t *val);
int32_t {{ codec_chip | capitalize }}DeviceRegWrite(uint32_t reg, uint32_t value);
int32_t {{ codec_chip.upper() }}CodecReadReg(const struct CodecDevice *codec, uint32_t reg, uint32_t *val);
int32_t {{ codec_chip | capitalize }}CodecWriteReg(const struct CodecDevice *codec, uint32_t reg, uint32_t value);
int32_t {{ codec_chip | capitalize }}RegBitsUpdate(struct AudioMixerControl regAttr);
int32_t {{ codec_chip | capitalize }}DaiDeviceInit(struct AudioCard *card, const struct DaiDevice *device);
int32_t {{ codec_chip | capitalize }}DaiStartup(const struct AudioCard *card, const struct DaiDevice *device);
int32_t {{ codec_chip | capitalize }}DaiHwParams(const struct AudioCard *card, const struct AudioPcmHwParams *param);
int32_t {{ codec_chip | capitalize }}NormalTrigger(const struct AudioCard *card, int cmd, const struct DaiDevice *device);
int32_t {{ codec_chip.upper() }}CodecDaiReadReg(const struct DaiDevice *dai, uint32_t reg, uint32_t *value);
int32_t {{ codec_chip.upper() }}CodecDaiWriteReg(const struct DaiDevice *dai, uint32_t reg, uint32_t value);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif

#endif
