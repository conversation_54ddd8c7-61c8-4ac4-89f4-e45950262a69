/*
 * Copyright (c) 2021 Rockchip Electronics Co., Ltd.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */

#ifndef __{{ compatible_driver.upper() }}_CODEC_H__
#define __{{ compatible_driver.upper() }}_CODEC_H__

/* codec register */
#define {{ compatible_driver.upper() }}_CODEC_BASE        0x0000

//需要的寄存器地址，结构体等
//示例如下：

struct platform_device *GetCodecPlatformDevice(void);
struct regmap_config getCodecRegmap(void);
#endif /* __{{ compatible_driver.upper() }}_CODEC_H__ */