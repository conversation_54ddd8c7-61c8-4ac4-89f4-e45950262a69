/*
 * Copyright (c) 2021 Rockchip Electronics Co., Ltd.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */

#include <linux/device.h>
#include <linux/module.h>
#include <linux/of.h>
#include <linux/platform_device.h>
/* Include the necessary header files for the codec chip */
//#include <linux/mfd/rk808.h>
#include "{{ compatible_driver }}_codec.h"
#include "audio_driver_log.h"

#define HDF_LOG_TAG "{{ codec_chip }}_codec_linux_driver"

/*
* 在这里你有两种选择去实现底层的linux驱动，一种是利用linux regmap.
* 第二种方法是借助具体的驱动来实现，例如i2c。
* i2c的实现主要是完成i2c驱动的注册
* static struct i2c_driver es8323_i2c_driver = {
* 	.driver = {
* 		.name = "ES8323",
* 		.of_match_table = of_match_ptr(es8323_of_match),
* 		},
* 	.probe = es8323_i2c_probe,
* 	.remove = es8323_i2c_remove,
* 	.id_table = es8323_i2c_id,
* };
* module_i2c_driver(es8323_i2c_driver);
* 这里具体给出regmap的函数级实现
*/

struct platform_device *{{ compatible_driver }}_pdev;
struct platform_device *GetCodecPlatformDevice(void)
{
    return {{ compatible_driver }}_pdev;
}

static const struct of_device_id {{ compatible_driver }}_codec_dt_ids[] = {
    { .compatible = "rockchip,{{ compatible_driver }}-codec" },
};
MODULE_DEVICE_TABLE(of, {{ compatible_driver }}_codec_dt_ids);

static int {{ compatible_driver }}_platform_probe(struct platform_device *pdev)
{
    {{ compatible_driver }}_pdev = pdev;
    dev_info(&pdev->dev, "got {{ compatible_driver }}-codec platform_device");
    return 0;
}

static int {{ compatible_driver }}_platform_remove(struct platform_device *pdev)
{
    panic("%s not support now", __func__);
}

static struct platform_driver {{ compatible_driver }}_codec_driver = {
    .driver = {
        .name = "{{ compatible_driver }}-codec",
        .of_match_table = {{ compatible_driver }}_codec_dt_ids,
    },
    .probe = {{ compatible_driver }}_platform_probe,
    .remove = {{ compatible_driver }}_platform_remove,
};

module_platform_driver({{ compatible_driver }}_codec_driver);

static const struct reg_default {{ compatible_driver }}_reg_defaults[] = {
    /*
    * Default register configuration for the audio codec chip.
    * Registers are declared in {{ compatible_driver }}_codec.h.
    * You can declare like this:
    * { REGISTER, VALUE },
    * e.g.
    * { RK817_CODEC_DTOP_VUCTIME, 0x03 },
    */

};

static bool {{ compatible_driver }}_volatile_register(struct device *dev, unsigned int reg)
{
    switch (reg) {
        case RK817_CODEC_DTOP_LPT_SRST:
            return true;
        default:
            return false;
    }
}

static bool {{ compatible_driver }}_codec_register(struct device *dev, unsigned int reg)
{
    switch (reg) {
        /*
        * 该函数验证传入reg的有效性，在这里你需要去填写具体的寄存器名称.
        * 例如：
        * case RK817_CODEC_DTOP_VUCTIME:
        */
            return true;
        default:
            return false;
    }
}
/*
* 根据自己的需求修改regmap设置
*/
static const struct regmap_config {{ compatible_driver }}_codec_regmap_config = {
    .name = "{{ compatible_driver }}-codec",
    .reg_bits = 8,
    .val_bits = 8,
    .reg_stride = 1,
    .max_register = 0x4f,
    .cache_type = REGCACHE_FLAT,
    .volatile_reg = {{ compatible_driver }}_volatile_register,
    .writeable_reg = {{ compatible_driver }}_codec_register,
    .readable_reg = {{ compatible_driver }}_codec_register,
    .reg_defaults = {{ compatible_driver }}_reg_defaults,
    .num_reg_defaults = ARRAY_SIZE({{ compatible_driver }}_reg_defaults),
};

struct regmap_config getCodecRegmap(void)
{
    return {{ compatible_driver }}_codec_regmap_config;
}
