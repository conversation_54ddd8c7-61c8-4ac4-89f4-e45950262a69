/*
 * Copyright (c) 2022 Institute of Software, CAS.
 * Author : <PERSON><PERSON><PERSON>@nj.iscas.ac.cn
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <linux/device.h>
#include <linux/module.h>
#include <linux/of.h>
#include <linux/platform_device.h>
/* Include the necessary header files for the codec chip */
// #include <linux/mfd/rk808.h> // This is specific, might need templating if it changes
#include "{{ compatible_driver }}_codec.h"
#include "{{ codec_chip }}_codec_impl.h"
#include "audio_codec_if.h"
#include "audio_codec_base.h"
#include "audio_driver_log.h"

#define HDF_LOG_TAG "{{ codec_chip }}_codec_adapter"


struct CodecData g_{{ codec_chip }}Data = {
    .Init = {{ codec_chip | capitalize }}DeviceInit,
    .Read = {{ codec_chip | upper }}CodecReadReg,
    .Write = {{ codec_chip | capitalize }}CodecWriteReg,
};

struct AudioDaiOps g_{{ codec_chip }}DaiDeviceOps = {
    .Startup = {{ codec_chip | capitalize }}DaiStartup,
    .HwParams = {{ codec_chip | capitalize }}DaiHwParams,
    .Trigger = {{ codec_chip | capitalize }}NormalTrigger,
};

struct DaiData g_{{ codec_chip }}DaiData = {
    .DaiInit = {{ codec_chip | capitalize }}DaiDeviceInit,
    .Read = {{ codec_chip | upper }}CodecDaiReadReg,
    .Write = {{ codec_chip | upper }}CodecDaiWriteReg,
    .ops = &g_{{ codec_chip }}DaiDeviceOps,
};

static struct {{ codec_chip | capitalize }}ChipData *g_chip;
struct {{ codec_chip | capitalize }}ChipData* GetCodecDevice(void)
{
    return g_chip;
}

/* HdfDriverEntry implementations */
static int32_t {{ codec_chip | capitalize }}DriverBind(struct HdfDeviceObject *device)
{
    struct CodecHost *codecHost;
    if (device == NULL) {
        AUDIO_DRIVER_LOG_ERR("input para is NULL.");
        return HDF_FAILURE;
    }

    codecHost = (struct CodecHost *)OsalMemCalloc(sizeof(*codecHost));
    if (codecHost == NULL) {
        AUDIO_DRIVER_LOG_ERR("malloc codecHost fail!");
        return HDF_FAILURE;
    }
    codecHost->device = device;
    device->service = &codecHost->service;

    return HDF_SUCCESS;
}

static int32_t {{ codec_chip | capitalize }}DriverInit(struct HdfDeviceObject *device)
{
    int32_t ret;
    struct regmap_config codecRegmapCfg = getCodecRegmap();
    struct platform_device *codeDev = GetCodecPlatformDevice();
    struct rk808 *rk808 = NULL; // This part is specific to rk808 MFD
    if (!codeDev) {
        AUDIO_DEVICE_LOG_ERR("codeDev not ready");
        return HDF_FAILURE;
    }
    g_chip = devm_kzalloc(&codeDev->dev, sizeof(struct {{ codec_chip | capitalize }}ChipData), GFP_KERNEL);
    if (!g_chip) {
        AUDIO_DEVICE_LOG_ERR("devm_kzalloc for g_chip failed!");
        return HDF_ERR_MALLOC_FAIL;
    }
    g_chip->codec = g_{{ codec_chip }}Data;
    g_chip->dai = g_{{ codec_chip }}DaiData;
    platform_set_drvdata(codeDev, g_chip);
    g_chip->pdev = codeDev;

    // This block is highly specific to the rk808 multi-function device (MFD).
    // If you use a different MFD or a standalone I2C device, this will need to be changed.
    rk808 = dev_get_drvdata(g_chip->pdev->dev.parent);
    if (!rk808) {
        AUDIO_DEVICE_LOG_ERR("Failed to get rk808 parent device data");
        return HDF_FAILURE;
    }

    g_chip->regmap = devm_regmap_init_i2c(rk808->i2c, &codecRegmapCfg);
    if (IS_ERR(g_chip->regmap)) {
        AUDIO_DEVICE_LOG_ERR("failed to allocate regmap: %ld\n", PTR_ERR(g_chip->regmap));
        return HDF_FAILURE;
    }

    if (CodecDaiGetPortConfigInfo(device, &g_{{ codec_chip }}DaiData) != HDF_SUCCESS) {
        return HDF_FAILURE;
    }

    if (CodecGetConfigInfo(device, &g_{{ codec_chip }}Data) !=  HDF_SUCCESS) {
        return HDF_FAILURE;
    }
    if (CodecSetConfigInfoOfControls(&g_{{ codec_chip }}Data,  &g_{{ codec_chip }}DaiData) != HDF_SUCCESS) {
        return HDF_FAILURE;
    }
    ret = CodecGetServiceName(device, &(g_{{ codec_chip }}Data.drvCodecName));
    if (ret != HDF_SUCCESS) {
        return ret;
    }
    ret = CodecGetDaiName(device,  &(g_{{ codec_chip }}DaiData.drvDaiName));
    if (ret != HDF_SUCCESS) {
        return HDF_FAILURE;
    }
    OsalMutexInit(&g_{{ codec_chip }}Data.mutex);
    OsalMutexInit(&g_{{ codec_chip }}DaiData.mutex);
    ret = AudioRegisterCodec(device, &g_{{ codec_chip }}Data, &g_{{ codec_chip }}DaiData);
    if (ret != HDF_SUCCESS) {
        return ret;
    }
    return HDF_SUCCESS;
}

static void {{ codec_chip | capitalize }}DriverRelease(struct HdfDeviceObject *device)
{
    struct CodecHost *codecHost;
    if (device == NULL) {
        AUDIO_DRIVER_LOG_ERR("device is NULL");
        return;
    }

    // The global g_chip is used, which is not ideal for multi-instance scenarios.
    if (g_chip) {
        platform_set_drvdata(g_chip->pdev, NULL);
        // regmap is managed by devm, no need to explicitly exit
        // devm_kfree is also managed, but explicit call is here.
        if (g_chip->regmap) {
            regmap_exit(g_chip->regmap);
        }
        devm_kfree(&g_chip->pdev->dev, g_chip);
    }
    OsalMutexDestroy(&g_{{ codec_chip }}Data.mutex);
    OsalMutexDestroy(&g_{{ codec_chip }}DaiData.mutex);

    if (device->priv != NULL) {
        OsalMemFree(device->priv);
    }
    codecHost = (struct CodecHost *)device->service;
    if (codecHost == NULL) {
        HDF_LOGE("CodecDriverRelease: codecHost is NULL");
        return;
    }
    OsalMemFree(codecHost);
}

/* HdfDriverEntry definitions */
struct HdfDriverEntry g_{{ codec_chip | capitalize }}DriverEntry = {
    .moduleVersion = 1,
    .moduleName = "CODEC_{{ codec_chip | upper }}",
    .Bind = {{ codec_chip | capitalize }}DriverBind,
    .Init = {{ codec_chip | capitalize }}DriverInit,
    .Release = {{ codec_chip | capitalize }}DriverRelease,
};

HDF_INIT(g_{{ codec_chip | capitalize }}DriverEntry);
