/*
 * Copyright (c) 2022 Institute of Software, CAS.
 * Author : h<PERSON><PERSON>@nj.iscas.ac.cn
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include <linux/regmap.h>
#include "gpio_if.h"
#include "linux/of_gpio.h"
#include "audio_driver_log.h"
#include "audio_stream_dispatch.h"
#include "audio_codec_base.h"
#include "audio_sapm.h"
#include "{{ compatible_driver }}_codec.h"
#include "{{ codec_chip }}_codec_impl.h"

#define HDF_LOG_TAG "{{ codec_chip }}_codec"

/* {{ codec_chip | upper }} I2C Device address  */
#define {{ codec_chip | upper }}_I2C_DEV_ADDR   (0x20)
#define {{ codec_chip | upper }}_I2C_BUS_NUMBER   (0)      // i2c0
#define {{ codec_chip | upper }}_I2C_WAIT_TIMES   (10)     // ms
#define {{ codec_chip | upper }}_CODEC_REG_MAX    (0x4f)
#define {{ codec_chip | upper }}_REG_INDEX0       0
#define {{ codec_chip | upper }}_REG_INDEX1       1
#define {{ codec_chip | upper }}_REG_INDEX2       2
#define {{ codec_chip | upper }}_REG_INDEX3       3
#define {{ codec_chip | upper }}_REG_INDEX4       4
#define {{ codec_chip | upper }}_REG_INDEX5       5
#define {{ codec_chip | upper }}_REG_INDEX6       6
#define {{ codec_chip | upper }}_REG_INDEX7       7

struct {{ codec_chip | capitalize }}TransferData {
    uint16_t i2cDevAddr;
    uint16_t i2cBusNumber;
    uint32_t CfgCtrlCount;
    struct AudioRegCfgGroupNode **RegCfgGroupNode;
    struct AudioKcontrol *Controls;
};

static const struct AudioSapmRoute g_audioRoutes[] = {
    { "SPKL", NULL, "SPKL PGA"},
    { "HPL", NULL, "HPL PGA"},
    { "HPR", NULL, "HPR PGA"},
    { "SPKL PGA", "Speaker1 Switch", "DAC1"},
    { "HPL PGA", "Headphone1 Switch", "DAC2"},
    { "HPR PGA", "Headphone2 Switch", "DAC3"},

    { "ADCL", NULL, "LPGA"},
    { "ADCR", NULL, "RPGA"},
    { "LPGA", "LPGA MIC Switch", "MIC1"},
    { "RPGA", "RPGA MIC Switch", "MIC2"},
};

struct RegDefaultVal {
        uint16_t reg;
        uint16_t val;
};

struct RegConfig {
    const struct RegDefaultVal *regVal;
    uint32_t size;
};

static const struct RegDefaultVal g_{{ compatible_driver }}RenderStartRegDefaults[] = {
    { {{ compatible_driver | upper }}_CODEC_DDAC_POPD_DACST, 0x04 },
    { {{ compatible_driver | upper }}_CODEC_DI2S_RXCMD_TSD, 0x20 },
};

static const struct RegConfig g_{{ compatible_driver }}RenderStartRegConfig = {
    .regVal = g_{{ compatible_driver }}RenderStartRegDefaults,
    .size = ARRAY_SIZE(g_{{ compatible_driver }}RenderStartRegDefaults),
};

static const struct RegDefaultVal g_{{ compatible_driver }}RenderStopRegDefaults[] = {
    { {{ compatible_driver | upper }}_CODEC_DDAC_POPD_DACST, 0x06 },
    { {{ compatible_driver | upper }}_CODEC_DI2S_RXCMD_TSD, 0x10 },
};

static const struct RegConfig g_{{ compatible_driver }}RenderStopRegConfig = {
    .regVal = g_{{ compatible_driver }}RenderStopRegDefaults,
    .size = ARRAY_SIZE(g_{{ compatible_driver }}RenderStopRegDefaults),
};

static const struct RegDefaultVal g_{{ compatible_driver }}CaptureStartRegDefaults[] = {
    { {{ compatible_driver | upper }}_CODEC_DTOP_DIGEN_CLKE, 0xff },   // I2SRX_EN I2SRX_CKE ADC_EN  ADC_CKE
    { {{ compatible_driver | upper }}_CODEC_DI2S_TXCR3_TXCMD, 0x88 },
};

static const struct RegConfig g_{{ compatible_driver }}CaptureStartRegConfig = {
    .regVal = g_{{ compatible_driver }}CaptureStartRegDefaults,
    .size = ARRAY_SIZE(g_{{ compatible_driver }}CaptureStartRegDefaults),
};

static const struct RegDefaultVal g_{{ compatible_driver }}CaptureStopRegDefaults[] = {
    { {{ compatible_driver | upper }}_CODEC_DTOP_DIGEN_CLKE, 0x0f },
    { {{ compatible_driver | upper }}_CODEC_DI2S_TXCR3_TXCMD, 0x40 },
};

static const struct RegConfig g_{{ compatible_driver }}CaptureStopRegConfig = {
    .regVal = g_{{ compatible_driver }}CaptureStopRegDefaults,
    .size = ARRAY_SIZE(g_{{ compatible_driver }}CaptureStopRegDefaults),
};

int32_t {{ codec_chip | capitalize }}DeviceRegRead(uint32_t reg, uint32_t *val)
{
    struct {{ codec_chip | capitalize }}ChipData *chip = GetCodecDevice();
    if (chip == NULL) {
        AUDIO_DRIVER_LOG_ERR("get codec device failed.");
        return HDF_FAILURE;
    }

    if (regmap_read(chip->regmap, reg, val)) {
        AUDIO_DRIVER_LOG_ERR("read register fail: [%x]", reg);
        return HDF_FAILURE;
    }
    return HDF_SUCCESS;
}

int32_t {{ codec_chip | capitalize }}DeviceRegWrite(uint32_t reg, uint32_t value)
{
    struct {{ codec_chip | capitalize }}ChipData *chip = GetCodecDevice();
    if (chip == NULL) {
        AUDIO_DRIVER_LOG_ERR("get codec device failed.");
        return HDF_FAILURE;
    }

    if (regmap_write(chip->regmap, reg, value)) {
        AUDIO_DRIVER_LOG_ERR("write register fail: [%x] = %x", reg, value);
        return HDF_FAILURE;
    }
    return HDF_SUCCESS;
}

int32_t {{ codec_chip | upper }}CodecDaiReadReg(const struct DaiDevice *dai, uint32_t reg, uint32_t *value)
{
    if (value == NULL) {
        AUDIO_DRIVER_LOG_ERR("param val is null.");
        return HDF_FAILURE;
    }
    if (reg > {{ codec_chip | upper }}_CODEC_REG_MAX) {
        AUDIO_DRIVER_LOG_ERR("codec dai read error reg: %x ", reg);
        return HDF_FAILURE;
    }

    if ({{ codec_chip | capitalize }}DeviceRegRead(reg, value)) {
        AUDIO_DRIVER_LOG_ERR("codec dai read register fail: [%04x]", reg);
        return HDF_FAILURE;
    }

    return HDF_SUCCESS;
}
int32_t {{ codec_chip | upper }}CodecDaiWriteReg(const struct DaiDevice *dai, uint32_t reg, uint32_t value)
{
    if (reg > {{ codec_chip | upper }}_CODEC_REG_MAX) {
        AUDIO_DRIVER_LOG_ERR("codec dai write error reg: %x ", reg);
        return HDF_FAILURE;
    }

    if ({{ codec_chip | capitalize }}DeviceRegWrite(reg, value)) {
        AUDIO_DRIVER_LOG_ERR("codec dai write register fail: [%04x] = %04x", reg, value);
        return HDF_FAILURE;
    }
    return HDF_SUCCESS;
}

static const {{ codec_chip | upper }}SampleRateTimes {{ codec_chip | upper }}GetSRT(const uint32_t rate)
{
    switch (rate) {
        case AUDIO_SAMPLE_RATE_8000:
            return {{ codec_chip | upper }}_SRT_00;
        case AUDIO_SAMPLE_RATE_16000:
            return {{ codec_chip | upper }}_SRT_01;
        case AUDIO_SAMPLE_RATE_32000:
        case AUDIO_SAMPLE_RATE_44100:
        case AUDIO_SAMPLE_RATE_48000:
            return {{ codec_chip | upper }}_SRT_02;
        case AUDIO_SAMPLE_RATE_96000:
            return {{ codec_chip | upper }}_SRT_03;
        default:
            AUDIO_DEVICE_LOG_DEBUG("unsupport samplerate %d\n", rate);
            return {{ codec_chip | upper }}_SRT_02;
    }
}

static const {{ codec_chip | upper }}PLLInputCLKPreDIV {{ codec_chip | upper }}GetPremode(const uint32_t rate)
{
    switch (rate) {
        case AUDIO_SAMPLE_RATE_8000:
            return {{ codec_chip | upper }}_PREMODE_1;
        case AUDIO_SAMPLE_RATE_16000:
            return {{ codec_chip | upper }}_PREMODE_2;
        case AUDIO_SAMPLE_RATE_32000:
        case AUDIO_SAMPLE_RATE_44100:
        case AUDIO_SAMPLE_RATE_48000:
            return {{ codec_chip | upper }}_PREMODE_3;
        case AUDIO_SAMPLE_RATE_96000:
            return {{ codec_chip | upper }}_PREMODE_4;
        default:
            AUDIO_DEVICE_LOG_DEBUG("unsupport samplerate %d\n", rate);
            return {{ codec_chip | upper }}_PREMODE_3;
    }
}

static const {{ codec_chip | upper }}_VDW {{ codec_chip | upper }}GetI2SDataWidth(const uint32_t bitWidth)
{
    switch (bitWidth) {
        case BIT_WIDTH16:
            return {{ codec_chip | upper }}_VDW_16BITS;
        case BIT_WIDTH24:
        case BIT_WIDTH32:
            return {{ codec_chip | upper }}_VDW_24BITS;
        default:
            AUDIO_DEVICE_LOG_ERR("unsupport sample bit width %d.\n", bitWidth);
            return {{ codec_chip | upper }}_VDW_16BITS;
    }
}

int32_t {{ codec_chip | upper }}UpdateRenderParams(struct DaiDevice *codecDai, struct AudioRegCfgGroupNode **regCfgGroup,
    struct {{ codec_chip.upper() }}DaiParamsVal codecDaiParamsVal)
{
    int32_t ret;
    struct AudioMixerControl *regAttr = NULL;
    int32_t itemNum;

    if (regCfgGroup == NULL || regCfgGroup[AUDIO_DAI_PATAM_GROUP] == NULL
        || regCfgGroup[AUDIO_DAI_PATAM_GROUP]->regCfgItem == NULL) {
        AUDIO_DEVICE_LOG_ERR("input invalid parameter.");
        return HDF_FAILURE;
    }

    itemNum = regCfgGroup[AUDIO_DAI_PATAM_GROUP]->itemNum;
    regAttr = regCfgGroup[AUDIO_DAI_PATAM_GROUP]->regCfgItem;
    if (regAttr == NULL) {
        AUDIO_DEVICE_LOG_ERR("reg Cfg Item is null.");
        return HDF_FAILURE;
    }

    regAttr[{{ codec_chip | upper }}_REG_INDEX0].value = {{ codec_chip | upper }}GetPremode(codecDaiParamsVal.frequencyVal);
    ret = AudioDaiRegUpdate(codecDai, &regAttr[{{ codec_chip | upper }}_REG_INDEX0]);
    if (ret != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("set freq failed.");
        return HDF_FAILURE;
    }

    regAttr[{{ codec_chip | upper }}_REG_INDEX5].value = 0;
    ret = AudioDaiRegUpdate(codecDai, &regAttr[{{ codec_chip | upper }}_REG_INDEX5]);
    if (ret != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("set freq failed.");
        return HDF_FAILURE;
    }

    regAttr[{{ codec_chip | upper }}_REG_INDEX1].value = {{ codec_chip | upper }}GetSRT(codecDaiParamsVal.frequencyVal);
    ret = AudioDaiRegUpdate(codecDai, &regAttr[{{ codec_chip | upper }}_REG_INDEX1]);
    if (ret != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("set freq failed.");
        return HDF_FAILURE;
    }

    regAttr[{{ codec_chip | upper }}_REG_INDEX5].value = 0xf;
    ret = AudioDaiRegUpdate(codecDai, &regAttr[{{ codec_chip | upper }}_REG_INDEX5]);
    if (ret != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("set freq failed.");
        return HDF_FAILURE;
    }

    regAttr[{{ codec_chip | upper }}_REG_INDEX4].value = {{ codec_chip | upper }}GetI2SDataWidth(codecDaiParamsVal.DataWidthVal);
    ret = AudioDaiRegUpdate(codecDai, &regAttr[{{ codec_chip | upper }}_REG_INDEX4]);
    if (ret != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("set freq failed.");
        return HDF_FAILURE;
    }

    return HDF_SUCCESS;
}

int32_t {{ codec_chip | upper }}UpdateCaptureParams(struct DaiDevice *codecDai, struct AudioRegCfgGroupNode **regCfgGroup,
    struct {{ codec_chip.upper() }}DaiParamsVal codecDaiParamsVal)
{
    int32_t ret;
    struct AudioMixerControl *regAttr = NULL;
    int32_t itemNum;

    if (regCfgGroup == NULL || regCfgGroup[AUDIO_DAI_PATAM_GROUP] == NULL
        || regCfgGroup[AUDIO_DAI_PATAM_GROUP]->regCfgItem == NULL) {
        AUDIO_DEVICE_LOG_ERR("input invalid parameter.");
        return HDF_FAILURE;
    }

    itemNum = regCfgGroup[AUDIO_DAI_PATAM_GROUP]->itemNum;
    regAttr = regCfgGroup[AUDIO_DAI_PATAM_GROUP]->regCfgItem;
    if (regAttr == NULL) {
        AUDIO_DEVICE_LOG_ERR("reg Cfg Item is null.");
        return HDF_FAILURE;
    }

    regAttr[{{ codec_chip | upper }}_REG_INDEX0].value = {{ codec_chip | upper }}GetPremode(codecDaiParamsVal.frequencyVal);
    ret = AudioDaiRegUpdate(codecDai, &regAttr[{{ codec_chip | upper }}_REG_INDEX0]);
    if (ret != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("set freq failed.");
        return HDF_FAILURE;
    }

    regAttr[{{ codec_chip | upper }}_REG_INDEX6].value = 0x0;
    ret = AudioDaiRegUpdate(codecDai, &regAttr[{{ codec_chip | upper }}_REG_INDEX6]);
    if (ret != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("set freq failed.");
        return HDF_FAILURE;
    }

    regAttr[{{ codec_chip | upper }}_REG_INDEX2].value = {{ codec_chip | upper }}GetSRT(codecDaiParamsVal.frequencyVal);
    ret = AudioDaiRegUpdate(codecDai, &regAttr[{{ codec_chip | upper }}_REG_INDEX2]);
    if (ret != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("set freq failed.");
        return HDF_FAILURE;
    }

    regAttr[{{ codec_chip | upper }}_REG_INDEX6].value = 0xf;
    ret = AudioDaiRegUpdate(codecDai, &regAttr[{{ codec_chip | upper }}_REG_INDEX6]);
    if (ret != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("set freq failed.");
        return HDF_FAILURE;
    }

    regAttr[{{ codec_chip | upper }}_REG_INDEX3].value = {{ codec_chip | upper }}GetI2SDataWidth(codecDaiParamsVal.DataWidthVal);
    ret = AudioDaiRegUpdate(codecDai, &regAttr[{{ codec_chip | upper }}_REG_INDEX3]);
    if (ret != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("set freq failed.");
        return HDF_FAILURE;
    }

    return HDF_SUCCESS;
}

int32_t {{ codec_chip | upper }}DaiParamsUpdate(struct DaiDevice *codecDai, enum AudioStreamType streamType,
    struct {{ codec_chip.upper() }}DaiParamsVal codecDaiParamsVal)
{
    int32_t ret;
    struct AudioRegCfgGroupNode **regCfgGroup = NULL;

    if (codecDai == NULL || codecDai->devData == NULL) {
        AUDIO_DEVICE_LOG_ERR("input invalid parameter.");
        return HDF_FAILURE;
    }
    regCfgGroup = codecDai->devData->regCfgGroup;

    if (regCfgGroup == NULL || regCfgGroup[AUDIO_DAI_PATAM_GROUP] == NULL
        || regCfgGroup[AUDIO_DAI_PATAM_GROUP]->regCfgItem == NULL) {
        AUDIO_DEVICE_LOG_ERR("regCfgGroup is invalid.");
        return HDF_FAILURE;
    }

    if (streamType == AUDIO_RENDER_STREAM) {
        ret = {{ codec_chip | upper }}UpdateRenderParams(codecDai, regCfgGroup, codecDaiParamsVal);
        if (ret != HDF_SUCCESS) {
            AUDIO_DEVICE_LOG_ERR("{{ codec_chip | upper }}UpdateRenderParams failed.");
            return HDF_FAILURE;
        }
    } else if (streamType == AUDIO_CAPTURE_STREAM) {
        ret = {{ codec_chip | upper }}UpdateCaptureParams(codecDai, regCfgGroup, codecDaiParamsVal);
        if (ret != HDF_SUCCESS) {
            AUDIO_DEVICE_LOG_ERR("{{ codec_chip | upper }}UpdateCaptureParams failed.");
            return HDF_FAILURE;
        }
    } else {
        AUDIO_DEVICE_LOG_ERR("streamType is invalid.");
        return HDF_FAILURE;
    }
    return HDF_SUCCESS;
}

int32_t {{ codec_chip | upper }}CodecReadReg(const struct CodecDevice *codec, uint32_t reg, uint32_t *val)
{
    if (val == NULL) {
        AUDIO_DRIVER_LOG_ERR("param val is null.");
        return HDF_FAILURE;
    }
    if (reg > {{ codec_chip | upper }}_CODEC_REG_MAX) {
        AUDIO_DRIVER_LOG_ERR("read error reg: %x ", reg);
        return HDF_FAILURE;
    }

    if ({{ codec_chip | capitalize }}DeviceRegRead(reg, val)) {
        AUDIO_DRIVER_LOG_ERR("read register fail: [%04x]", reg);
        return HDF_FAILURE;
    }
    return HDF_SUCCESS;
}

int32_t {{ codec_chip | capitalize }}CodecWriteReg(const struct CodecDevice *codec, uint32_t reg, uint32_t value)
{
    if (reg > {{ codec_chip | upper }}_CODEC_REG_MAX) {
        AUDIO_DRIVER_LOG_ERR("write error reg: %x ", reg);
        return HDF_FAILURE;
    }
    if ({{ codec_chip | capitalize }}DeviceRegWrite(reg, value)) {
        AUDIO_DRIVER_LOG_ERR("write register fail: [%04x] = %04x", reg, value);
        return HDF_FAILURE;
    }
    return HDF_SUCCESS;
}

int32_t {{ codec_chip | capitalize }}DeviceInit(struct AudioCard *audioCard, const struct CodecDevice *device)
{
    int32_t ret;

    if (audioCard == NULL || device == NULL || device->devData == NULL ||
        device->devData->sapmComponents == NULL || device->devData->controls == NULL) {
        AUDIO_DRIVER_LOG_ERR("input para is NULL.");
        return HDF_ERR_INVALID_OBJECT;
    }

    ret = CodecDeviceInitRegConfig(device);
    if (ret != HDF_SUCCESS) {
        AUDIO_DEVICE_LOG_ERR("{{ codec_chip | capitalize }}RegDefaultInit failed.");
        return HDF_FAILURE;
    }

    if (AudioAddControls(audioCard, device->devData->controls, device->devData->numControls) != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("add controls failed.");
        return HDF_FAILURE;
    }

    if (AudioSapmNewComponents(audioCard, device->devData->sapmComponents,
        device->devData->numSapmComponent) != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("new components failed.");
        return HDF_FAILURE;
    }

    if (AudioSapmAddRoutes(audioCard, g_audioRoutes, HDF_ARRAY_SIZE(g_audioRoutes)) != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("add route failed.");
        return HDF_FAILURE;
    }

    if (AudioSapmNewControls(audioCard) != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("add sapm controls failed.");
        return HDF_FAILURE;
    }

    if (AudioSapmSleep(audioCard) != HDF_SUCCESS) {
        AUDIO_DRIVER_LOG_ERR("add sapm sleep modular failed.");
        return HDF_FAILURE;
    }

    AUDIO_DRIVER_LOG_DEBUG("success.");
    return HDF_SUCCESS;
}

int32_t {{ codec_chip | capitalize }}DaiDeviceInit(struct AudioCard *card, const struct DaiDevice *device)
{
    if (device == NULL || device->devDaiName == NULL) {
        AUDIO_DEVICE_LOG_ERR("input para is NULL.");
        return HDF_FAILURE;
    }
    (void)card;
    AUDIO_DEVICE_LOG_DEBUG("success.");
    return HDF_SUCCESS;
}

int32_t {{ codec_chip | capitalize }}DaiStartup(const struct AudioCard *card, const struct DaiDevice *device)
{
    (void)card;
    (void)device;

    AUDIO_DRIVER_LOG_DEBUG("success.");
    return HDF_SUCCESS;
}

int32_t {{ codec_chip | capitalize }}FormatToBitWidth(enum AudioFormat format, uint32_t *bitWidth)
{
    if (bitWidth == NULL) {
        AUDIO_DRIVER_LOG_ERR("bitWidth is null.");
        return HDF_FAILURE;
    }
    switch (format) {
        case AUDIO_FORMAT_TYPE_PCM_16_BIT:
            *bitWidth = DATA_BIT_WIDTH16;
            break;

        case AUDIO_FORMAT_TYPE_PCM_24_BIT:
            *bitWidth = DATA_BIT_WIDTH24;
            break;

        default:
            AUDIO_DRIVER_LOG_ERR("format: %d is not support.", format);
            return HDF_FAILURE;
    }
    return HDF_SUCCESS;
}


int32_t {{ codec_chip | capitalize }}DaiHwParams(const struct AudioCard *card, const struct AudioPcmHwParams *param)
{
    int32_t ret;
    uint32_t bitWidth;
    struct {{ codec_chip.upper() }}DaiParamsVal codecDaiParamsVal;
    (void)card;

    if (param == NULL || param->cardServiceName == NULL || card == NULL ||
        card->rtd == NULL || card->rtd->codecDai == NULL || card->rtd->codecDai->devData == NULL ||
        card->rtd->codecDai->devData->regCfgGroup == NULL) {
        AUDIO_DRIVER_LOG_ERR("input para is NULL.");
        return HDF_FAILURE;
    }

    ret = {{ codec_chip | capitalize }}FormatToBitWidth(param->format, &bitWidth);
    if (ret != HDF_SUCCESS) {
        return HDF_FAILURE;
    }

    codecDaiParamsVal.frequencyVal = param->rate;
    codecDaiParamsVal.DataWidthVal = bitWidth;

    AUDIO_DRIVER_LOG_DEBUG("channels count : %d .", param->channels);
    ret =  {{ codec_chip | upper }}DaiParamsUpdate(card->rtd->codecDai, param->streamType, codecDaiParamsVal);
    if (ret != HDF_SUCCESS) {
        AUDIO_DEVICE_LOG_ERR("{{ codec_chip | upper }}DaiParamsUpdate failed.");
        return HDF_FAILURE;
    }

    return HDF_SUCCESS;
}

int32_t {{ codec_chip | upper }}DeviceRegConfig(const struct RegConfig regConfig)
{
    int32_t index;
    int32_t ret;

    for (index = 0; index < regConfig.size; index++) {
        ret = {{ codec_chip | capitalize }}DeviceRegWrite(regConfig.regVal[index].reg, regConfig.regVal[index].val);
        if (ret != HDF_SUCCESS) {
            AUDIO_DEVICE_LOG_ERR("{{ codec_chip | capitalize }}DeviceRegWrite failed.");
            return HDF_FAILURE;
        }
    }

    return HDF_SUCCESS;
}


/* normal scene */
int32_t {{ codec_chip | capitalize }}NormalTrigger(const struct AudioCard *card, int32_t cmd, const struct DaiDevice *device)
{
    int32_t ret;
    switch (cmd) {
        case AUDIO_DRV_PCM_IOCTL_RENDER_START:
        case AUDIO_DRV_PCM_IOCTL_RENDER_RESUME:
            ret = {{ codec_chip | upper }}DeviceRegConfig(g_{{ compatible_driver }}RenderStartRegConfig);
            if (ret != HDF_SUCCESS) {
                AUDIO_DEVICE_LOG_ERR("{{ codec_chip | upper }}DeviceRegConfig failed.");
                return HDF_FAILURE;
            }
            break;

        case AUDIO_DRV_PCM_IOCTL_RENDER_STOP:
        case AUDIO_DRV_PCM_IOCTL_RENDER_PAUSE:
            ret = {{ codec_chip | upper }}DeviceRegConfig(g_{{ compatible_driver }}RenderStopRegConfig);
            if (ret != HDF_SUCCESS) {
                AUDIO_DEVICE_LOG_ERR("{{ codec_chip | upper }}DeviceRegConfig failed.");
                return HDF_FAILURE;
            }
            break;

        case AUDIO_DRV_PCM_IOCTL_CAPTURE_START:
        case AUDIO_DRV_PCM_IOCTL_CAPTURE_RESUME:
            ret = {{ codec_chip | upper }}DeviceRegConfig(g_{{ compatible_driver }}CaptureStartRegConfig);
            if (ret != HDF_SUCCESS) {
                AUDIO_DEVICE_LOG_ERR("{{ codec_chip | upper }}DeviceRegConfig failed.");
                return HDF_FAILURE;
            }
            break;

        case AUDIO_DRV_PCM_IOCTL_CAPTURE_STOP:
        case AUDIO_DRV_PCM_IOCTL_CAPTURE_PAUSE:
            ret = {{ codec_chip | upper }}DeviceRegConfig(g_{{ compatible_driver }}CaptureStopRegConfig);
            if (ret != HDF_SUCCESS) {
                AUDIO_DEVICE_LOG_ERR("{{ codec_chip | upper }}DeviceRegConfig failed.");
                return HDF_FAILURE;
            }
            break;

        default:
            break;
    }

    return HDF_SUCCESS;
}