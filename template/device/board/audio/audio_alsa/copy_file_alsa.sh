#!/bin/bash

# 检查环境变量
if [ -z "$TEMPLATE_OHOS_PATH" ]; then
    echo "错误: TEMPLATE_OHOS_PATH 环境变量未设置"
    exit 1
fi

if [ -z "$TEMPLATE_PRODUCT_NAME" ]; then
    echo "错误: TEMPLATE_PRODUCT_NAME 环境变量未设置"
    exit 1
fi

# 源目录路径（需要根据实际的OpenHarmony目录结构调整）
# 根据需要更改路径，以rk3568为基础模板
SOURCE_DIR="$TEMPLATE_OHOS_PATH/device/board/hihope/rk3568/audio_alsa"

# 检查源目录是否存在
if [ ! -d "$SOURCE_DIR" ]; then
    echo "警告: 未找到audio_alsa源目录，尝试的路径: $SOURCE_DIR"
    echo "跳过文件复制"
    exit 0
fi

# 目标目录（输出目录下对应的位置）
# 构建输出目录路径：output/{ohos_version}/{product_name}/device/audio_alsa
if [ -z "$TEMPLATE_OHOS_VERSION" ]; then
    TEMPLATE_OHOS_VERSION="5.0.0"
fi

# 项目根目录从命令行参数获取，如果没有提供则尝试自动计算
if [ -n "$1" ]; then
    PROJECT_ROOT="$1"
    echo "使用传入的项目根目录: $PROJECT_ROOT"
else
    # 回退到自动计算（兼容旧的调用方式）
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../../.." && pwd)"
    echo "自动计算项目根目录: $PROJECT_ROOT"
fi

# 构建目标目录路径 - Board 目录结构
TARGET_DIR="$PROJECT_ROOT/output/$TEMPLATE_OHOS_VERSION/$TEMPLATE_PRODUCT_NAME/board/audio_alsa"

# 确保目标目录存在
mkdir -p "$TARGET_DIR"

echo "目标目录: $TARGET_DIR"
echo "从 $SOURCE_DIR 复制audio_alsa文件到 $TARGET_DIR"

# 复制所有文件和目录
if [ -d "$SOURCE_DIR" ]; then
    # 复制目录内容，但不复制目录本身
    cp -r "$SOURCE_DIR"/* "$TARGET_DIR/" 2>/dev/null || {
        echo "警告: 复制过程中出现错误，可能是权限问题或源目录为空"
    }
    echo "audio_alsa文件复制完成"
else
    echo "警告: 源目录不存在: $SOURCE_DIR"
    echo "创建空的audio_alsa目录作为占位符"
    # 即使源目录不存在，也创建目标目录（已经在上面创建了）
    echo "audio_alsa目录创建完成"
fi

echo "脚本执行完成，目标目录: $TARGET_DIR"
