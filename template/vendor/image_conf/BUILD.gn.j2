# <AUTHOR> <EMAIL>
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

ohos_copy("all_image_conf") {
  sources = [
    "system_image_conf.txt",
    "updater_ramdisk_image_conf.txt",
    "userdata_image_conf.txt",
    "vendor_image_conf.txt",
    "sys_prod_image_conf.txt",
    "chip_prod_image_conf.txt",
  ]
  outputs = [ "${product_output_dir}/imagesconf/{{source_file_part}}" ]
}

group("custom_image_conf") {
    deps = [
      ":all_image_conf",
    ]
}
