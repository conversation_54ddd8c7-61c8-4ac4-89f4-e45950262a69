#!/bin/bash


COPY_SUBDIRS=(
    "khdf"
    "uhdf"
)

# 检查环境变量
if [ -z "$TEMPLATE_OHOS_PATH" ]; then
    echo "错误: TEMPLATE_OHOS_PATH 环境变量未设置"
    exit 1
fi

if [ -z "$TEMPLATE_PRODUCT_NAME" ]; then
    echo "错误: TEMPLATE_PRODUCT_NAME 环境变量未设置"
    exit 1
fi

# Source directory path (needs to be adjusted according to the actual OpenHarmony directory structure)
# Change the path as needed, using rk3568 as the base template
SOURCE_BASE_DIR="$TEMPLATE_OHOS_PATH/vendor/$TEMPLATE_DEVICE_COMPANY/$TEMPLATE_PRODUCT_NAME/hdf_config"

# 检查源基础目录是否存在
if [ ! -d "$SOURCE_BASE_DIR" ]; then
    echo "警告: 未找到hdf_config源目录，尝试的路径: $SOURCE_BASE_DIR"
    echo "跳过文件复制"
    exit 0
fi

# 目标目录（输出目录下对应的位置）
# 构建输出目录路径：output/{ohos_version}/{product_name}/device/camera
if [ -z "$TEMPLATE_OHOS_VERSION" ]; then
    TEMPLATE_OHOS_VERSION="5.0.0"
fi

# 项目根目录从命令行参数获取，如果没有提供则尝试自动计算
if [ -n "$1" ]; then
    PROJECT_ROOT="$1"
    echo "使用传入的项目根目录: $PROJECT_ROOT"
else
    # 回退到自动计算（兼容旧的调用方式）
    # 脚本路径: template/device/board/camera/copy_file.sh
    # 需要向上4级到达项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"
    echo "自动计算项目根目录: $PROJECT_ROOT"
fi

# 构建目标目录路径 - Vendor 目录结构
TARGET_DIR="$PROJECT_ROOT/output/$TEMPLATE_OHOS_VERSION/$TEMPLATE_PRODUCT_NAME/vendor/$TEMPLATE_DEVICE_COMPANY/$TEMPLATE_PRODUCT_NAME/hdf_config"

# 确保目标目录存在
mkdir -p "$TARGET_DIR"

echo "目标目录: $TARGET_DIR"
echo "从 $SOURCE_BASE_DIR 复制指定的hdf_config子目录到 $TARGET_DIR"

# 复制指定的子目录
copied_count=0
for subdir in "${COPY_SUBDIRS[@]}"; do
    source_subdir="$SOURCE_BASE_DIR/$subdir"
    target_subdir="$TARGET_DIR/$subdir"

    if [ -d "$source_subdir" ]; then
        echo "复制子目录: $subdir"
        # 确保目标子目录存在
        mkdir -p "$target_subdir"
        # 复制子目录内容
        cp -r "$source_subdir"/* "$target_subdir/" 2>/dev/null || {
            echo "警告: 复制子目录 $subdir 时出现错误"
        }
        ((copied_count++))
    else
        echo "警告: 源子目录不存在: $source_subdir"
    fi
done

if [ $copied_count -gt 0 ]; then
    echo "hdf_config文件复制完成，成功复制 $copied_count 个子目录"
else
    echo "警告: 没有找到任何指定的子目录"
    echo "创建空的hdf_config目录作为占位符"
fi

echo "脚本执行完成，目标目录: $TARGET_DIR"
