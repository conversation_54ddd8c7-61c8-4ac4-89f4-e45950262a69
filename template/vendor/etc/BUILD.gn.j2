# Copyright (c) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

ohos_prebuilt_etc("para_for_chip_prod") {
  source = "./param/hardware_{{ product_name }}.para"
  install_images = [ chip_prod_base_dir ]
  relative_install_dir = "param"
  part_name = "product_{{ product_name }}"
}

ohos_prebuilt_etc("para_for_sys_prod") {
  source = "./param/product_{{ product_name }}.para"
  install_images = [ sys_prod_base_dir ]
  relative_install_dir = "param"
  part_name = "product_{{ product_name }}"
}

group("product_etc_conf") {
  deps = [
    ":para_for_chip_prod",
    ":para_for_sys_prod",
  ]
}
