# Copyright (c) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

ohos_prebuilt_etc("res_sched_config") {
  source = "./ressched/res_sched_config.xml"
  install_enable = true
  install_images = [ chipset_base_dir ]
  module_install_dir = "etc/ressched"
  part_name = "product_rk3568"
}

ohos_prebuilt_etc("res_sched_plugin_switch") {
  source = "./ressched/res_sched_plugin_switch.xml"
  install_enable = true
  install_images = [ chipset_base_dir ]
  module_install_dir = "etc/ressched"
  part_name = "product_rk3568"
}

ohos_prebuilt_etc("cgroup_action_config") {
  source = "./cgroup_sched/cgroup_action_config.json"
  install_enable = true
  install_images = [ chipset_base_dir ]
  module_install_dir = "etc/cgroup_sched"
  part_name = "product_rk3568"
}

ohos_prebuilt_etc("socperf_boost_config") {
  source = "./soc_perf/socperf_boost_config.xml"
  install_enable = true
  install_images = [ chipset_base_dir ]
  module_install_dir = "etc/soc_perf"
  part_name = "product_rk3568"
}

ohos_prebuilt_etc("socperf_resource_config") {
  source = "./soc_perf/socperf_resource_config.xml"
  install_enable = true
  install_images = [ chipset_base_dir ]
  module_install_dir = "etc/soc_perf"
  part_name = "product_rk3568"
}

group("resourceschedule") {
  deps = [
    ":cgroup_action_config",
    ":res_sched_config",
    ":res_sched_plugin_switch",
    ":socperf_boost_config",
    ":socperf_resource_config",
  ]
}
