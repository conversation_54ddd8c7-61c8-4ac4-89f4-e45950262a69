# Copyright (c) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

ohos_prebuilt_etc("install_list_config") {
  source = "./install_list.json"
  relative_install_dir = "app"
  part_name = "product_rk3568"
}

ohos_prebuilt_etc("uninstall_list_config") {
  source = "./uninstall_list.json"
  relative_install_dir = "app"
  part_name = "product_rk3568"
}

ohos_prebuilt_etc("install_list_capability_config") {
  source = "./install_list_capability.json"
  relative_install_dir = "app"
  part_name = "product_rk3568"
}

ohos_prebuilt_etc("install_list_permissions_config") {
  source = "./install_list_permissions.json"
  relative_install_dir = "app"
  part_name = "product_rk3568"
}

group("preinstall-config") {
  deps = [
    ":install_list_capability_config",
    ":install_list_config",
    ":install_list_permissions_config",
    ":uninstall_list_config",
  ]
}
