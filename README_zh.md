## ✨ 简化设计

本项目采用简化设计，将所有核心功能整合到一个类中：

- **`simplified_generator.py`**: 集成所有功能的核心生成器
- **`simple_cli.py`**: 简洁易用的命令行接口
- **清晰的工作流程**: 易于理解和维护

### 设计优势

1. **代码结构清晰**: 所有核心逻辑集中在一个类中，易于理解
2. **减少冗余**: 消除重复代码和复杂的类间调用
3. **易于扩展**: 添加新功能只需修改一个文件
4. **简单易用**: 提供便捷函数，一行代码即可完成生成
5. **高性能**: 启动快速，内存占用低

### 快速开始

#### 1. 使用命令行工具

```bash
# 基础用法
python -m oh_codegen orangepi_5b.yaml

# 清理输出目录
python -m oh_codegen orangepi_5b.yaml --clean

# 详细输出
python -m oh_codegen orangepi_5b.yaml --verbose

# 预览模式（不实际生成文件）
python -m oh_codegen orangepi_5b.yaml --preview
```

#### 2. 在Python代码中使用

```python
from oh_codegen import generate_device_code

# 简单使用
result = generate_device_code("orangepi_5b.yaml")

if result['success']:
    print(f"生成成功！生成了 {len(result['generated_files'])} 个文件")
else:
    print("生成失败:")
    for error in result['errors']:
        print(f"  - {error}")
```

#### 3. 高级使用

```python
from oh_codegen import SimpleCodeGenerator

# 创建生成器实例
generator = SimpleCodeGenerator()

# 加载配置
generator.load_config("orangepi_5b.yaml")

# 查看启用的模块
enabled_modules = generator.get_enabled_modules()
print(f"启用的模块: {enabled_modules}")

# 执行生成
result = generator.generate_code("orangepi_5b.yaml", clean_output=True)
```

## 工作流程

代码生成器的工作流程非常直观：

1. **加载配置**: 读取YAML配置文件，解析产品信息和模块配置
2. **准备输出目录**: 根据产品名称创建输出目录
3. **处理启用的模块**: 遍历每个启用的模块目录
4. **递归处理目录**: 对每个目录执行以下操作：
   - 如果存在`copy_file.sh`脚本，先执行脚本
   - 处理目录中的所有`.j2`模板文件
   - 递归处理子目录
5. **生成文件**: 将渲染后的内容写入输出文件

## 配置文件格式

配置文件使用YAML格式，示例：

```yaml
ohos_version: 5.0.0
product_name: orangepi_5b
device_company: orangepi
target_cpu: arm64
soc: rk3588s
soc_company: rockchip

board_code:
  - audio_drivers:
      enabled: true
      adm_drivers:
        enable: true
        codec_chip: rk809
        compatible_driver: rk817
        bus_type: platform
      alsa_drivers:
        enable: false
        codec_chip: es8323
        implementation_method: manual
  - camera_drivers:
      enabled: true
      camera_chip: ov5640
      node:
        - facenode
```

## 模板变量

配置文件中的所有字段都会被转换为模板变量，可以在Jinja2模板中使用：

- `product_name`: 产品名称
- `device_company`: 设备厂商
- `soc`: SoC型号
- `audio_drivers_enabled`: 音频驱动是否启用
- `audio_drivers_adm_drivers_codec_chip`: 音频编解码器芯片
- 等等...

## 模板文件

模板文件使用Jinja2语法，文件名以`.j2`结尾：

```c
// 示例：audio_driver.c.j2
#include <stdio.h>

#define PRODUCT_NAME "{{ product_name }}"
#define SOC_TYPE "{{ soc }}"

{% if audio_drivers_enabled %}
// 音频驱动代码
void init_audio_driver(void) {
    printf("Initializing audio driver for %s\n", PRODUCT_NAME);
    {% if audio_drivers_adm_drivers_enable %}
    init_adm_driver("{{ audio_drivers_adm_drivers_codec_chip }}");
    {% endif %}
}
{% endif %}
```


# 代码替换配置：config/rk3568.custom.yaml
version: "1.0"                    # 版本号（必需）
product_name: "rk3568"           # 产品名称（必需，与主配置一致）

code_modifications:              # 代码修改配置（必需）
  
  # 函数替换配置
  function_replacements:
    - file_path: "相对路径"       # 目标文件路径（必需）
      function_signature: "函数签名"  # 完整函数签名（必需）
      description: "描述"         # 替换说明（可选）
      replacement_code: |         # 替换代码（必需）
        // 新的函数实现
      includes:                   # 头文件包含（可选）
        - "#include \"header.h\""
  
  # 函数添加配置
  function_additions:
    - file_path: "相对路径"       # 目标文件路径（必需）
      position: "插入位置"        # 插入位置（必需）
      description: "描述"         # 添加说明（可选）
      code: |                     # 添加代码（必需）
        // 新增的函数
      includes:                   # 头文件包含（可选）
        - "#include \"header.h\""
  
  # 头文件修改配置
  header_modifications:
    - file_path: "头文件路径"     # 目标头文件路径（必需）
      description: "描述"         # 修改说明（可选）
      additions:                  # 添加内容列表（必需）
        - position: "插入位置"    # 插入位置（必需）
          code: |                 # 添加代码（必需）
            // 新增的声明


