# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# 代码生成输出
output/
# 防止在template目录下意外创建output目录
template/output/
**/output/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.bak
.DS_Store
Thumbs.db

# 配置文件（如果包含敏感信息）
# config/local_*.yaml
# config/private_*.yaml